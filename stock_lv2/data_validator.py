"""
Data validation module for stock data quality assurance.
Inspired by Context7's robust data handling practices.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import logging
from datetime import datetime

from config import get_config, DataValidationConfig

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """Result of data validation with details about issues found."""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    cleaned_data: Optional[pd.DataFrame] = None
    stats: Dict[str, Any] = None


class DataValidator:
    """
    Comprehensive data validator for stock market data.
    Provides schema validation, quality checks, and data cleaning.
    """
    
    def __init__(self, config: Optional[DataValidationConfig] = None):
        """
        Initialize the data validator.
        
        Args:
            config: Data validation configuration. If None, uses global config.
        """
        self.config = config or get_config().validation
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def validate_stock_data(self, data: pd.DataFrame, stock_code: str) -> ValidationResult:
        """
        Validate stock data for a single stock.
        
        Args:
            data: Stock data DataFrame
            stock_code: Stock code for context in error messages
            
        Returns:
            ValidationResult with validation status and cleaned data
        """
        errors = []
        warnings = []
        
        if not self.config.enable_validation:
            return ValidationResult(
                is_valid=True,
                errors=[],
                warnings=["Validation disabled in configuration"],
                cleaned_data=data.copy()
            )
        
        # Check if data is empty
        if data.empty:
            errors.append(f"Stock {stock_code}: No data available")
            return ValidationResult(is_valid=False, errors=errors, warnings=warnings)
        
        # Validate required columns
        required_cols = get_config().analysis.required_columns
        missing_cols = [col for col in required_cols if col not in data.columns]
        if missing_cols:
            errors.append(f"Stock {stock_code}: Missing required columns: {missing_cols}")
        
        # Create a copy for cleaning
        cleaned_data = data.copy()
        
        # Validate data types and ranges
        numeric_columns = ['最高', '最低', '开盘', '收盘', '成交量']
        for col in numeric_columns:
            if col in cleaned_data.columns:
                # Check for non-numeric values
                non_numeric = pd.to_numeric(cleaned_data[col], errors='coerce').isna()
                if non_numeric.any():
                    warnings.append(f"Stock {stock_code}: Non-numeric values in {col} column")
                    cleaned_data[col] = pd.to_numeric(cleaned_data[col], errors='coerce')
                
                # Check price ranges
                if col in ['最高', '最低', '开盘', '收盘']:
                    invalid_prices = (
                        (cleaned_data[col] < self.config.min_price) |
                        (cleaned_data[col] > self.config.max_price)
                    )
                    if invalid_prices.any():
                        warnings.append(
                            f"Stock {stock_code}: {invalid_prices.sum()} invalid prices in {col}"
                        )
                        cleaned_data = cleaned_data[~invalid_prices]
        
        # Validate price relationships
        if all(col in cleaned_data.columns for col in ['最高', '最低', '开盘', '收盘']):
            # High should be >= Low
            invalid_high_low = cleaned_data['最高'] < cleaned_data['最低']
            if invalid_high_low.any():
                warnings.append(
                    f"Stock {stock_code}: {invalid_high_low.sum()} rows where 最高 < 最低"
                )
                cleaned_data = cleaned_data[~invalid_high_low]
            
            # High and Low should contain Open and Close
            invalid_range = (
                (cleaned_data['开盘'] > cleaned_data['最高']) |
                (cleaned_data['开盘'] < cleaned_data['最低']) |
                (cleaned_data['收盘'] > cleaned_data['最高']) |
                (cleaned_data['收盘'] < cleaned_data['最低'])
            )
            if invalid_range.any():
                warnings.append(
                    f"Stock {stock_code}: {invalid_range.sum()} rows with invalid price ranges"
                )
                cleaned_data = cleaned_data[~invalid_range]
        
        # Check for extreme price changes
        if '收盘' in cleaned_data.columns and len(cleaned_data) > 1:
            price_changes = cleaned_data['收盘'].pct_change().abs() * 100
            extreme_changes = price_changes > self.config.max_price_change_percent
            if extreme_changes.any():
                warnings.append(
                    f"Stock {stock_code}: {extreme_changes.sum()} extreme price changes "
                    f"(>{self.config.max_price_change_percent}%)"
                )
        
        # Outlier detection
        if self.config.enable_outlier_detection and '收盘' in cleaned_data.columns:
            outliers = self._detect_outliers(cleaned_data['收盘'], stock_code)
            if outliers.any():
                warnings.append(f"Stock {stock_code}: {outliers.sum()} potential outliers detected")
        
        # Remove rows with missing critical data
        critical_cols = ['收盘']
        before_count = len(cleaned_data)
        cleaned_data = cleaned_data.dropna(subset=critical_cols)
        after_count = len(cleaned_data)
        
        if before_count != after_count:
            warnings.append(
                f"Stock {stock_code}: Removed {before_count - after_count} rows with missing critical data"
            )
        
        # Calculate statistics
        stats = self._calculate_stats(cleaned_data, stock_code)
        
        # Final validation
        is_valid = len(errors) == 0 and len(cleaned_data) >= get_config().analysis.min_trading_days
        
        if not is_valid and len(errors) == 0:
            errors.append(
                f"Stock {stock_code}: Insufficient data after cleaning "
                f"({len(cleaned_data)} < {get_config().analysis.min_trading_days} required days)"
            )
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            cleaned_data=cleaned_data if is_valid else None,
            stats=stats
        )
    
    def _detect_outliers(self, series: pd.Series, stock_code: str) -> pd.Series:
        """
        Detect outliers using statistical methods.
        
        Args:
            series: Pandas series to check for outliers
            stock_code: Stock code for logging
            
        Returns:
            Boolean series indicating outliers
        """
        if len(series) < 3:
            return pd.Series([False] * len(series), index=series.index)
        
        # Use IQR method for outlier detection
        Q1 = series.quantile(0.25)
        Q3 = series.quantile(0.75)
        IQR = Q3 - Q1
        
        lower_bound = Q1 - self.config.outlier_threshold * IQR
        upper_bound = Q3 + self.config.outlier_threshold * IQR
        
        outliers = (series < lower_bound) | (series > upper_bound)
        
        if outliers.any():
            self.logger.debug(
                f"Stock {stock_code}: Outlier bounds [{lower_bound:.2f}, {upper_bound:.2f}], "
                f"found {outliers.sum()} outliers"
            )
        
        return outliers
    
    def _calculate_stats(self, data: pd.DataFrame, stock_code: str) -> Dict[str, Any]:
        """
        Calculate statistics for the cleaned data.
        
        Args:
            data: Cleaned stock data
            stock_code: Stock code
            
        Returns:
            Dictionary with statistics
        """
        stats = {
            'stock_code': stock_code,
            'total_rows': len(data),
            'date_range': None,
            'price_stats': {}
        }
        
        if not data.empty:
            if hasattr(data.index, 'min') and hasattr(data.index, 'max'):
                stats['date_range'] = {
                    'start': str(data.index.min()),
                    'end': str(data.index.max())
                }
            
            # Price statistics
            if '收盘' in data.columns:
                close_prices = data['收盘']
                stats['price_stats'] = {
                    'mean': float(close_prices.mean()),
                    'std': float(close_prices.std()),
                    'min': float(close_prices.min()),
                    'max': float(close_prices.max()),
                    'median': float(close_prices.median())
                }
        
        return stats
    
    def validate_batch(self, stock_data_dict: Dict[str, pd.DataFrame]) -> Dict[str, ValidationResult]:
        """
        Validate a batch of stock data.
        
        Args:
            stock_data_dict: Dictionary mapping stock codes to DataFrames
            
        Returns:
            Dictionary mapping stock codes to ValidationResults
        """
        results = {}
        
        for stock_code, data in stock_data_dict.items():
            try:
                results[stock_code] = self.validate_stock_data(data, stock_code)
            except Exception as e:
                self.logger.error(f"Validation failed for stock {stock_code}: {e}")
                results[stock_code] = ValidationResult(
                    is_valid=False,
                    errors=[f"Validation error: {str(e)}"],
                    warnings=[]
                )
        
        return results
    
    def get_validation_summary(self, results: Dict[str, ValidationResult]) -> Dict[str, Any]:
        """
        Generate a summary of validation results.
        
        Args:
            results: Dictionary of validation results
            
        Returns:
            Summary statistics
        """
        total_stocks = len(results)
        valid_stocks = sum(1 for r in results.values() if r.is_valid)
        total_errors = sum(len(r.errors) for r in results.values())
        total_warnings = sum(len(r.warnings) for r in results.values())
        
        return {
            'total_stocks': total_stocks,
            'valid_stocks': valid_stocks,
            'invalid_stocks': total_stocks - valid_stocks,
            'success_rate': valid_stocks / total_stocks if total_stocks > 0 else 0,
            'total_errors': total_errors,
            'total_warnings': total_warnings,
            'timestamp': datetime.now().isoformat()
        }
