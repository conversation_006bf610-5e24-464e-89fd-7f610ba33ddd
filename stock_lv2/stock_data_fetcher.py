"""
Enhanced stock data fetcher with retry logic and error handling.
Inspired by Context7's robust API handling practices.
"""

import akshare as ak
import pandas as pd
import time
import logging
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import random
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading

from config import get_config, APIConfig


@dataclass
class FetchResult:
    """Result of a data fetch operation."""
    success: bool
    data: Optional[pd.DataFrame] = None
    error: Optional[str] = None
    attempts: int = 0
    duration: float = 0.0


class RateLimiter:
    """Thread-safe rate limiter for API calls."""
    
    def __init__(self, delay: float):
        self.delay = delay
        self.last_call = 0.0
        self.lock = threading.Lock()
    
    def wait(self):
        """Wait if necessary to respect rate limits."""
        with self.lock:
            now = time.time()
            time_since_last = now - self.last_call
            if time_since_last < self.delay:
                sleep_time = self.delay - time_since_last
                time.sleep(sleep_time)
            self.last_call = time.time()


class StockDataFetcher:
    """
    Enhanced stock data fetcher with retry logic, rate limiting, and error handling.
    """
    
    def __init__(self, config: Optional[APIConfig] = None):
        """
        Initialize the stock data fetcher.
        
        Args:
            config: API configuration. If None, uses global config.
        """
        self.config = config or get_config().api
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.rate_limiter = RateLimiter(self.config.rate_limit_delay)
        self.session_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_retries': 0,
            'start_time': datetime.now()
        }
    
    def get_stock_list(self) -> FetchResult:
        """
        Get the list of all A-share stocks with retry logic.
        
        Returns:
            FetchResult containing the stock list or error information
        """
        start_time = time.time()
        
        for attempt in range(1, self.config.max_retries + 1):
            try:
                self.rate_limiter.wait()
                self.session_stats['total_requests'] += 1
                
                self.logger.debug(f"Fetching stock list (attempt {attempt})")
                stock_list = ak.stock_info_a_code_name()
                
                if stock_list.empty:
                    raise ValueError("Received empty stock list")
                
                self.session_stats['successful_requests'] += 1
                duration = time.time() - start_time
                
                self.logger.info(f"Successfully fetched {len(stock_list)} stocks")
                return FetchResult(
                    success=True,
                    data=stock_list,
                    attempts=attempt,
                    duration=duration
                )
                
            except Exception as e:
                self.session_stats['failed_requests'] += 1
                error_msg = f"Attempt {attempt} failed: {str(e)}"
                self.logger.warning(error_msg)
                
                if attempt < self.config.max_retries:
                    self.session_stats['total_retries'] += 1
                    wait_time = self._calculate_backoff_delay(attempt)
                    self.logger.info(f"Retrying in {wait_time:.2f} seconds...")
                    time.sleep(wait_time)
                else:
                    duration = time.time() - start_time
                    final_error = f"Failed to fetch stock list after {attempt} attempts. Last error: {str(e)}"
                    self.logger.error(final_error)
                    return FetchResult(
                        success=False,
                        error=final_error,
                        attempts=attempt,
                        duration=duration
                    )
        
        # This should never be reached, but just in case
        return FetchResult(success=False, error="Unexpected error in retry loop")
    
    def get_stock_data(self, stock_code: str, start_date: str, end_date: str, 
                      period: str = "daily", adjust: str = "qfq") -> FetchResult:
        """
        Get historical data for a single stock with retry logic.
        
        Args:
            stock_code: Stock code
            start_date: Start date in YYYYMMDD format
            end_date: End date in YYYYMMDD format
            period: Data period (daily, weekly, monthly)
            adjust: Adjustment type (qfq, hfq, or empty)
            
        Returns:
            FetchResult containing the stock data or error information
        """
        start_time = time.time()
        
        for attempt in range(1, self.config.max_retries + 1):
            try:
                self.rate_limiter.wait()
                self.session_stats['total_requests'] += 1
                
                self.logger.debug(f"Fetching data for {stock_code} (attempt {attempt})")
                
                stock_data = ak.stock_zh_a_hist(
                    symbol=stock_code,
                    period=period,
                    start_date=start_date,
                    end_date=end_date,
                    adjust=adjust
                )
                
                self.session_stats['successful_requests'] += 1
                duration = time.time() - start_time
                
                self.logger.debug(f"Successfully fetched {len(stock_data)} rows for {stock_code}")
                return FetchResult(
                    success=True,
                    data=stock_data,
                    attempts=attempt,
                    duration=duration
                )
                
            except Exception as e:
                self.session_stats['failed_requests'] += 1
                error_msg = f"Stock {stock_code} attempt {attempt} failed: {str(e)}"
                self.logger.debug(error_msg)
                
                if attempt < self.config.max_retries:
                    self.session_stats['total_retries'] += 1
                    wait_time = self._calculate_backoff_delay(attempt)
                    time.sleep(wait_time)
                else:
                    duration = time.time() - start_time
                    final_error = f"Failed to fetch data for {stock_code} after {attempt} attempts. Last error: {str(e)}"
                    return FetchResult(
                        success=False,
                        error=final_error,
                        attempts=attempt,
                        duration=duration
                    )
        
        return FetchResult(success=False, error="Unexpected error in retry loop")
    
    def get_batch_stock_data(self, stock_codes: List[str], start_date: str, end_date: str,
                           max_workers: int = 5, progress_callback=None) -> Dict[str, FetchResult]:
        """
        Get historical data for multiple stocks with parallel processing.
        
        Args:
            stock_codes: List of stock codes
            start_date: Start date in YYYYMMDD format
            end_date: End date in YYYYMMDD format
            max_workers: Maximum number of concurrent workers
            progress_callback: Optional callback function for progress updates
            
        Returns:
            Dictionary mapping stock codes to FetchResults
        """
        results = {}
        total_stocks = len(stock_codes)
        completed = 0
        
        self.logger.info(f"Starting batch fetch for {total_stocks} stocks with {max_workers} workers")
        
        # For small batches or when rate limiting is strict, use sequential processing
        if total_stocks <= 10 or self.config.rate_limit_delay > 0.1:
            for i, stock_code in enumerate(stock_codes):
                result = self.get_stock_data(stock_code, start_date, end_date)
                results[stock_code] = result
                completed += 1
                
                if progress_callback:
                    progress_callback(completed, total_stocks, stock_code, result.success)
                
                # Progress logging
                if completed % 50 == 0 or completed == total_stocks:
                    success_rate = sum(1 for r in results.values() if r.success) / completed * 100
                    self.logger.info(f"Progress: {completed}/{total_stocks} ({success_rate:.1f}% success)")
        
        else:
            # Use parallel processing for larger batches
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit all tasks
                future_to_stock = {
                    executor.submit(self.get_stock_data, stock_code, start_date, end_date): stock_code
                    for stock_code in stock_codes
                }
                
                # Collect results as they complete
                for future in as_completed(future_to_stock):
                    stock_code = future_to_stock[future]
                    try:
                        result = future.result()
                        results[stock_code] = result
                        completed += 1
                        
                        if progress_callback:
                            progress_callback(completed, total_stocks, stock_code, result.success)
                        
                        # Progress logging
                        if completed % 50 == 0 or completed == total_stocks:
                            success_rate = sum(1 for r in results.values() if r.success) / completed * 100
                            self.logger.info(f"Progress: {completed}/{total_stocks} ({success_rate:.1f}% success)")
                            
                    except Exception as e:
                        self.logger.error(f"Unexpected error processing {stock_code}: {e}")
                        results[stock_code] = FetchResult(
                            success=False,
                            error=f"Unexpected error: {str(e)}"
                        )
                        completed += 1
        
        # Log final statistics
        successful = sum(1 for r in results.values() if r.success)
        self.logger.info(f"Batch fetch completed: {successful}/{total_stocks} successful")
        
        return results
    
    def _calculate_backoff_delay(self, attempt: int) -> float:
        """
        Calculate exponential backoff delay with jitter.
        
        Args:
            attempt: Current attempt number (1-based)
            
        Returns:
            Delay in seconds
        """
        base_delay = self.config.rate_limit_delay * (self.config.retry_backoff_factor ** (attempt - 1))
        # Add jitter to avoid thundering herd
        jitter = random.uniform(0.5, 1.5)
        return base_delay * jitter
    
    def get_session_stats(self) -> Dict[str, Any]:
        """
        Get statistics for the current session.
        
        Returns:
            Dictionary with session statistics
        """
        duration = (datetime.now() - self.session_stats['start_time']).total_seconds()
        success_rate = (
            self.session_stats['successful_requests'] / self.session_stats['total_requests']
            if self.session_stats['total_requests'] > 0 else 0
        )
        
        return {
            **self.session_stats,
            'session_duration': duration,
            'success_rate': success_rate,
            'requests_per_second': self.session_stats['total_requests'] / duration if duration > 0 else 0
        }
    
    def reset_stats(self):
        """Reset session statistics."""
        self.session_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_retries': 0,
            'start_time': datetime.now()
        }
