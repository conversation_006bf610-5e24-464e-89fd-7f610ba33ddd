"""
调试可转债API接口
"""

import akshare as ak
import pandas as pd

def 测试可转债API():
    """测试akshare的可转债相关API"""
    print("测试akshare可转债API...")

    try:
        # 测试获取可转债列表
        print("\n1. 测试获取可转债列表...")
        可转债列表 = ak.bond_cb_jsl()
        print(f"获取到 {len(可转债列表)} 只可转债")
        print("列名:", list(可转债列表.columns))
        print("前5行数据:")
        print(可转债列表.head())

        # 测试其他可能的可转债API
        print("\n2. 测试其他可转债API...")

        # 尝试获取可转债基本信息
        try:
            print("尝试 bond_cb_summary()...")
            summary_data = ak.bond_cb_summary()
            print(f"bond_cb_summary: {len(summary_data)} 行")
            if not summary_data.empty:
                print("列名:", list(summary_data.columns))
                print("前3行:")
                print(summary_data.head(3))
        except Exception as e:
            print(f"bond_cb_summary 失败: {e}")

        # 尝试获取可转债发行信息
        try:
            print("\n尝试 bond_cb_issue()...")
            issue_data = ak.bond_cb_issue()
            print(f"bond_cb_issue: {len(issue_data)} 行")
            if not issue_data.empty:
                print("列名:", list(issue_data.columns))
                print("前3行:")
                print(issue_data.head(3))
        except Exception as e:
            print(f"bond_cb_issue 失败: {e}")

        # 尝试获取可转债价格信息
        try:
            print("\n尝试 bond_cb_price()...")
            price_data = ak.bond_cb_price()
            print(f"bond_cb_price: {len(price_data)} 行")
            if not price_data.empty:
                print("列名:", list(price_data.columns))
                print("前3行:")
                print(price_data.head(3))
        except Exception as e:
            print(f"bond_cb_price 失败: {e}")

        # 尝试获取可转债基础数据
        try:
            print("\n尝试 bond_cb_adj()...")
            adj_data = ak.bond_cb_adj()
            print(f"bond_cb_adj: {len(adj_data)} 行")
            if not adj_data.empty:
                print("列名:", list(adj_data.columns))
                print("前3行:")
                print(adj_data.head(3))
        except Exception as e:
            print(f"bond_cb_adj 失败: {e}")

        # 尝试获取可转债列表（不同来源）
        try:
            print("\n尝试 bond_cb_list()...")
            list_data = ak.bond_cb_list()
            print(f"bond_cb_list: {len(list_data)} 行")
            if not list_data.empty:
                print("列名:", list(list_data.columns))
                print("前3行:")
                print(list_data.head(3))
        except Exception as e:
            print(f"bond_cb_list 失败: {e}")

        # 尝试获取可转债基本面数据
        try:
            print("\n尝试 bond_cb_profile()...")
            profile_data = ak.bond_cb_profile()
            print(f"bond_cb_profile: {len(profile_data)} 行")
            if not profile_data.empty:
                print("列名:", list(profile_data.columns))
                print("前3行:")
                print(profile_data.head(3))
        except Exception as e:
            print(f"bond_cb_profile 失败: {e}")

        # 检查akshare版本和可用的可转债相关函数
        print(f"\n3. akshare版本: {ak.__version__}")
        print("可转债相关函数:")
        bond_functions = [func for func in dir(ak) if 'bond_cb' in func]
        for func in bond_functions:
            print(f"  - {func}")

        # 测试实际可用的API
        print("\n4. 测试实际可用的API...")

        # 测试 bond_cb_summary_sina
        try:
            print("尝试 bond_cb_summary_sina()...")
            summary_sina = ak.bond_cb_summary_sina()
            print(f"bond_cb_summary_sina: {len(summary_sina)} 行")
            if not summary_sina.empty:
                print("列名:", list(summary_sina.columns))
                print("前3行:")
                print(summary_sina.head(3))
        except Exception as e:
            print(f"bond_cb_summary_sina 失败: {e}")

        # 测试 bond_cb_index_jsl
        try:
            print("\n尝试 bond_cb_index_jsl()...")
            index_jsl = ak.bond_cb_index_jsl()
            print(f"bond_cb_index_jsl: {len(index_jsl)} 行")
            if not index_jsl.empty:
                print("列名:", list(index_jsl.columns))
                print("前3行:")
                print(index_jsl.head(3))
        except Exception as e:
            print(f"bond_cb_index_jsl 失败: {e}")

        # 测试 bond_cb_profile_sina
        try:
            print("\n尝试 bond_cb_profile_sina()...")
            profile_sina = ak.bond_cb_profile_sina()
            print(f"bond_cb_profile_sina: {len(profile_sina)} 行")
            if not profile_sina.empty:
                print("列名:", list(profile_sina.columns))
                print("前3行:")
                print(profile_sina.head(3))
        except Exception as e:
            print(f"bond_cb_profile_sina 失败: {e}")

    except Exception as e:
        print(f"API测试失败: {e}")

if __name__ == "__main__":
    测试可转债API()
