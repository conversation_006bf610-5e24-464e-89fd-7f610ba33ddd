import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
import time
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_stock_data_retrieval(stock_codes=None, year=2024, month=12):
    """
    测试股票数据获取功能
    """
    if stock_codes is None:
        stock_codes = ['000001', '000002', '600000', '600036', '000858']  # 测试用股票代码
    
    start_date = f"{year}-{month:02d}-01"
    end_date = f"{year}-{month:02d}-{pd.Timestamp(start_date).days_in_month}"
    
    logger.info(f"测试数据获取 - 时间范围: {start_date} 至 {end_date}")
    
    for stock_code in stock_codes:
        try:
            logger.info(f"正在测试股票: {stock_code}")
            stock_df = ak.stock_zh_a_hist(symbol=stock_code, period="daily",
                                        start_date=start_date, end_date=end_date,
                                        adjust="qfq")
            
            if stock_df.empty:
                logger.warning(f"股票 {stock_code} 返回空数据")
                # 尝试不同的日期范围
                alt_end = (datetime.now() - timedelta(days=1)).strftime("%Y%m%d")
                alt_start = (datetime.now() - timedelta(days=30)).strftime("%Y%m%d")
                logger.info(f"尝试替代日期范围: {alt_start} 至 {alt_end}")
                
                stock_df = ak.stock_zh_a_hist(symbol=stock_code, period="daily",
                                            start_date=alt_start, end_date=alt_end,
                                            adjust="qfq")
            
            logger.info(f"股票 {stock_code} 数据行数: {len(stock_df)}")
            if not stock_df.empty:
                logger.info(f"列名: {list(stock_df.columns)}")
                logger.info(f"日期范围: {stock_df.index[0]} 至 {stock_df.index[-1]}")
            
            time.sleep(0.1)  # 避免API限制
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 数据失败: {str(e)}")

def get_top_10_amplitude_stocks(year=2024, month=12):
    """
    获取指定年月日均振幅前10的股票
    
    参数:
    year: 年份 (默认2024，避免未来日期)
    month: 月份 (默认12)
    
    返回:
    DataFrame: 包含股票代码、名称和日均振幅的前10股票
    """
    # 验证日期范围
    current_date = datetime.now()
    target_date = datetime(year, month, 1)
    
    if target_date > current_date:
        logger.warning(f"目标日期 {year}-{month} 是未来日期，调整为上个月")
        if current_date.month == 1:
            year = current_date.year - 1
            month = 12
        else:
            year = current_date.year
            month = current_date.month - 1
    
    # 1. 获取所有A股代码列表
    try:
        stock_info_a_code_name_df = ak.stock_info_a_code_name()
        all_stocks = stock_info_a_code_name_df['code'].tolist()
        logger.info(f"成功获取 {len(all_stocks)} 只股票代码")
    except Exception as e:
        logger.error(f"获取股票列表失败: {str(e)}")
        return pd.DataFrame()

    # 2. 设置时间范围
    start_date = f"{year}-{month:02d}-01"
    try:
        days_in_month = pd.Timestamp(start_date).days_in_month
        end_date = f"{year}-{month:02d}-{days_in_month:02d}"
    except:
        end_date = f"{year}-{month:02d}-28"  # 安全的月末日期
    
    logger.info(f"正在获取 {year}年{month}月 股票振幅数据...")
    logger.info(f"时间范围: {start_date} 至 {end_date}")

    # 3. 创建结果容器
    results = []
    successful_count = 0
    failed_count = 0

    # 4. 遍历股票获取数据
    for i, stock_code in enumerate(all_stocks[:100]):  # 限制数量用于测试
        try:
            # 获取股票日K线数据
            stock_df = ak.stock_zh_a_hist(symbol=stock_code, period="daily",
                                          start_date=start_date.replace('-', ''), 
                                          end_date=end_date.replace('-', ''),
                                          adjust="qfq")

            if stock_df.empty:
                logger.debug(f"股票 {stock_code} 在指定期间无数据")
                failed_count += 1
                continue
                
            # 验证必要的列是否存在
            required_cols = ['最高', '最低', '收盘']
            if not all(col in stock_df.columns for col in required_cols):
                logger.warning(f"股票 {stock_code} 缺少必要列: {stock_df.columns.tolist()}")
                failed_count += 1
                continue

            # 计算每日振幅
            stock_df['振幅'] = (stock_df['最高'] - stock_df['最低']) / stock_df['收盘'].shift(1)
            
            # 过滤无效振幅值
            valid_amplitude = stock_df['振幅'].dropna()
            if len(valid_amplitude) == 0:
                failed_count += 1
                continue
                
            # 计算月平均振幅
            avg_amplitude = valid_amplitude.mean() * 100

            # 获取股票名称
            stock_name_match = stock_info_a_code_name_df[stock_info_a_code_name_df['code'] == stock_code]['name']
            if stock_name_match.empty:
                stock_name = f"股票{stock_code}"
            else:
                stock_name = stock_name_match.values[0]

            # 添加到结果
            results.append({
                '股票代码': stock_code,
                '股票名称': stock_name,
                '日均振幅(%)': round(avg_amplitude, 2),
                '交易天数': len(valid_amplitude)
            })
            
            successful_count += 1

            # 打印进度
            if (i + 1) % 50 == 0:
                logger.info(f"已处理 {i + 1} 只股票，成功: {successful_count}，失败: {failed_count}")

            # API限制延迟
            time.sleep(0.05)

        except Exception as e:
            logger.error(f"获取 {stock_code} 数据失败: {str(e)}")
            failed_count += 1
            continue

    logger.info(f"数据获取完成 - 成功: {successful_count}，失败: {failed_count}")

    # 5. 创建DataFrame并排序
    if not results:
        logger.warning("未获取到任何有效数据")
        return pd.DataFrame()
        
    result_df = pd.DataFrame(results)

    # 6. 筛选和排序
    result_df = result_df[result_df['交易天数'] > 5]  # 降低最小交易天数要求
    if result_df.empty:
        logger.warning("筛选后无有效数据")
        return pd.DataFrame()
        
    result_df = result_df.sort_values(by='日均振幅(%)', ascending=False).head(10)
    
    logger.info(f"最终获得 {len(result_df)} 只股票的振幅数据")
    return result_df.reset_index(drop=True)


# 示例使用
if __name__ == "__main__":
    # 先测试数据获取
    logger.info("开始测试股票数据获取...")
    test_stock_data_retrieval()
    
    # 获取振幅数据
    top_10_amplitude = get_top_10_amplitude_stocks(year=2024, month=12)

    # 打印结果
    if not top_10_amplitude.empty:
        print(f"\n2024年12月日均振幅前{len(top_10_amplitude)}的股票:")
        print(top_10_amplitude[['股票代码', '股票名称', '日均振幅(%)', '交易天数']])

        # 保存结果到CSV
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"top_10_amplitude_stocks_{timestamp}.csv"
        top_10_amplitude.to_csv(filename, index=False, encoding='utf_8_sig')
        print(f"\n结果已保存到: {filename}")
    else:
        print("未找到符合条件的股票")
