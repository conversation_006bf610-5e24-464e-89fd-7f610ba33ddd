# Convertible Bond Market Data Analysis System (English Version)

## Overview

This is a convertible bond market data analysis system developed based on the Context7 enhanced architecture pattern, providing convertible bond daily amplitude analysis functionality. The system adopts a modular design with powerful error handling, data validation, and configuration management capabilities.

## Key Features

### 🎯 Core Analysis Functions
- **Convertible Bond Performance Indicator Analysis**: Calculate activity indicators based on daily price changes
- **Top N Selection**: Automatically filter the most active convertible bond targets
- **Data Quality Assessment**: Score data quality for each convertible bond
- **Multi-dimensional Indicators**: Include price, conversion premium rate, yield and other metrics

### 🛡️ Technical Features
- **Context7 Enhanced Architecture**: Inspired by Context7's modularity and configuration management concepts
- **Intelligent Retry Mechanism**: Exponential backoff retry logic to improve data retrieval success rate
- **Data Validation**: Comprehensive data quality checks and cleaning
- **Structured Logging**: Support JSON format logging for monitoring and debugging
- **Multi-format Output**: Support CSV, Excel and other output formats

## Quick Start

### Install Dependencies
```bash
pip install akshare pandas numpy
```

### Basic Usage

#### 1. Run Test
```bash
python convertible_bond_analyzer_en.py --test
```

#### 2. Analyze Convertible Bond Performance
```bash
# Analyze top 10 most active convertible bonds
python convertible_bond_analyzer_en.py

# Analyze top 15, limit processing to 20 convertible bonds
python convertible_bond_analyzer_en.py --max-bonds 20

# Enable debug logging
python convertible_bond_analyzer_en.py --log-level DEBUG
```

#### 3. Custom Configuration
```bash
# Use custom configuration file
python convertible_bond_analyzer_en.py --config convertible_bond_config_en.json

# Specify output directory
python convertible_bond_analyzer_en.py --output-dir /path/to/output
```

## Output Results

### Console Output
```
Top 10 Convertible Bonds by Daily Amplitude for 2025-06:
========================================================================================================================
Bond Code Bond Name  Daily Amplitude (%)  Trading Days  Average Price  Price Volatility (%)  Total Return (%)
404003    鸿达退债   87.19                1             2.48           -19.06                -87.19
404004    汇车退债   33.35                1             56.14          -15.79                33.35
123162    东杰转债   20.00                1             171.60         -2.44                 20.00
```

### Field Descriptions
- **Bond Code**: Unique identifier code for the convertible bond
- **Bond Name**: Chinese name of the convertible bond
- **Daily Amplitude (%)**: Activity indicator based on absolute value of daily price change
- **Trading Days**: Number of trading days covered by data (current version is 1 day)
- **Average Price**: Current price of the convertible bond
- **Price Volatility (%)**: Conversion premium rate (reflects conversion value)
- **Total Return (%)**: Daily price change
- **Data Quality Score**: Data quality score (between 0-1)

### CSV File Output
The system automatically generates timestamped CSV files saved in the `output` directory:
```
output/analysis_20250802_205147/convertible_bond_amplitude_analysis_top10_20250802_205147.csv
```

## Configuration

### Default Configuration File (convertible_bond_config_en.json)
```json
{
  "api": {
    "rate_limit_delay": 0.1,        // API call interval (seconds)
    "max_retries": 3,               // Maximum retry attempts
    "retry_backoff_factor": 2.0     // Retry backoff factor
  },
  "analysis": {
    "top_n_stocks": 10,             // Return top N count
    "amplitude_threshold": 0.0      // Activity threshold
  },
  "output": {
    "save_formats": ["csv", "excel"], // Output formats
    "include_timestamp": true        // Include timestamp in filename
  }
}
```

### Environment Variable Configuration
Configuration can be overridden via environment variables:
```bash
export STOCK_API_RATE_LIMIT=0.2
export STOCK_LOG_LEVEL=DEBUG
export STOCK_OUTPUT_DIR=/custom/output
```

## Command Line Arguments

| Argument | Description | Default |
|----------|-------------|---------|
| `--test` | Run data retrieval test only | - |
| `--year` | Analysis year | 2025 |
| `--month` | Analysis month | 6 |
| `--max-bonds` | Maximum number of bonds to process | Unlimited |
| `--config` | Configuration file path | Default config |
| `--output-dir` | Output directory | output |
| `--log-level` | Logging level | INFO |
| `--json-logs` | Enable JSON logging | false |

## Programmatic Usage

### Basic Usage
```python
from convertible_bond_analyzer_en import ConvertibleBondAnalysisSystem

# Initialize system
system = ConvertibleBondAnalysisSystem()

# Run test
test_result = system.test_data_retrieval()

# Get analysis results
results = system.get_top_amplitude_convertible_bonds(year=2025, month=6, max_bonds=20)
print(results)
```

### Backward Compatibility Functions
```python
# Compatible with original interface
from convertible_bond_analyzer_en import test_convertible_bond_data_retrieval, get_top_10_amplitude_convertible_bonds

# Test data retrieval
test_convertible_bond_data_retrieval()

# Get analysis results
results = get_top_10_amplitude_convertible_bonds(year=2025, month=6)
```

## Data Source

The system uses the akshare library to obtain convertible bond market data:
- **Data Source**: Jisilu convertible bond data
- **Update Frequency**: Real-time market data
- **Data Fields**: Include key indicators such as price, price change, conversion premium rate

## Technical Architecture

### Module Structure
```
convertible_bond_analyzer_en.py
├── ConvertibleBondConfig              # Configuration management
├── ConvertibleBondDataFetcher         # Data fetching with retry
├── ConvertibleBondDataValidator       # Data validation and cleaning
├── ConvertibleBondAnalyzer           # Indicator calculation and analysis
└── ConvertibleBondAnalysisSystem     # Main system coordinator
```

### Design Features
- **Modular Design**: Clear component responsibilities, easy to maintain and extend
- **Configuration Driven**: Control system behavior through configuration files
- **Error Recovery**: Intelligent retry and degradation handling
- **Data Quality**: Comprehensive data validation and quality assessment
- **Observability**: Detailed logging and performance monitoring

## Performance Characteristics

### Processing Capability
- **Data Retrieval**: Support batch retrieval with automatic rate limiting
- **Processing Speed**: Single analysis usually completed within 1 second
- **Memory Usage**: Optimized data processing with low memory footprint
- **Success Rate**: High success rate ensured through retry mechanism

### Monitoring Metrics
- **API Call Statistics**: Total requests, success rate, retry count
- **Data Quality**: Validation pass rate, data completeness
- **Processing Performance**: Processing time, memory usage

## Troubleshooting

### Common Issues

#### 1. Network Connection Issues
```
Error: Failed to get convertible bond market data
Solution: Check network connection or increase retry count
```

#### 2. Data Validation Failure
```
Error: Missing required columns
Solution: Check akshare version, ensure API compatibility
```

#### 3. Configuration File Error
```
Error: Configuration file loading failed
Solution: Check JSON format, ensure file path is correct
```

### Debugging Tips

#### Enable Verbose Logging
```bash
python convertible_bond_analyzer_en.py --log-level DEBUG --json-logs
```

#### Test Data Retrieval
```bash
python convertible_bond_analyzer_en.py --test
```

#### Limit Processing Count
```bash
python convertible_bond_analyzer_en.py --max-bonds 5
```

## File Structure

```
stock_lv2/
├── convertible_bond_analyzer_en.py          # Main analysis system (English)
├── convertible_bond_config_en.json          # English configuration file
├── test_convertible_bond_analyzer_en.py     # English test script
├── README_ConvertibleBond_EN.md             # This documentation
└── output/                                  # Analysis results output directory
    └── analysis_*/                          # Timestamped result directories
        └── convertible_bond_amplitude_analysis_top10_*.csv  # CSV result files
```

## Version History

### v1.0.0 (2025-08-02)
- Initial English version release
- Based on Context7 architecture design
- Support convertible bond market data analysis
- Implement modular architecture and configuration management
- Add data validation and quality assessment
- Support multiple output formats
- Full English interface and documentation

## License

This project is licensed under the MIT License.

## Contact

For questions or suggestions, please contact through:
- Submit issues to the project repository
- Send email to the development team
