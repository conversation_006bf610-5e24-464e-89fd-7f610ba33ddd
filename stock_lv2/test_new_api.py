#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的可转债API效果
Test the new convertible bond API effectiveness
"""

import akshare as ak
import pandas as pd
import time

def test_old_vs_new_api():
    """比较旧API和新API的数据量"""
    print("=" * 60)
    print("可转债API对比测试 / Convertible Bond API Comparison Test")
    print("=" * 60)
    
    # 测试旧API
    print("\n1. 测试旧API: ak.bond_cb_jsl()")
    try:
        start_time = time.time()
        old_data = ak.bond_cb_jsl()
        old_time = time.time() - start_time
        print(f"   ✓ 获取到 {len(old_data)} 只可转债")
        print(f"   ✓ 耗时: {old_time:.2f}秒")
        print(f"   ✓ 列名: {list(old_data.columns)}")
        print(f"   ✓ 前3行数据:")
        print(old_data.head(3))
    except Exception as e:
        print(f"   ✗ 失败: {e}")
        old_data = pd.DataFrame()
    
    # 测试新API
    print("\n2. 测试新API: ak.bond_zh_cov()")
    try:
        start_time = time.time()
        new_data = ak.bond_zh_cov()
        new_time = time.time() - start_time
        print(f"   ✓ 获取到 {len(new_data)} 只可转债")
        print(f"   ✓ 耗时: {new_time:.2f}秒")
        print(f"   ✓ 列名: {list(new_data.columns)}")
        print(f"   ✓ 前3行数据:")
        print(new_data.head(3))
    except Exception as e:
        print(f"   ✗ 失败: {e}")
        new_data = pd.DataFrame()
    
    # 对比结果
    print("\n3. 对比结果:")
    if not old_data.empty and not new_data.empty:
        print(f"   • 数据量提升: {len(old_data)} → {len(new_data)} (+{len(new_data) - len(old_data)}只)")
        print(f"   • 提升倍数: {len(new_data) / len(old_data):.1f}倍")
        print(f"   • 性能对比: 旧API {old_time:.2f}s vs 新API {new_time:.2f}s")
    
    # 分析新API数据质量
    if not new_data.empty:
        print("\n4. 新API数据质量分析:")
        print(f"   • 总可转债数量: {len(new_data)}")
        print(f"   • 有效债现价数量: {new_data['债现价'].notna().sum()}")
        print(f"   • 有效转股溢价率数量: {new_data['转股溢价率'].notna().sum()}")
        print(f"   • 转股溢价率范围: {new_data['转股溢价率'].min():.2f}% ~ {new_data['转股溢价率'].max():.2f}%")
        
        # 按转股溢价率排序，显示前10名最活跃的
        active_bonds = new_data.dropna(subset=['转股溢价率']).copy()
        active_bonds['活跃度'] = abs(active_bonds['转股溢价率'])
        top_active = active_bonds.nlargest(10, '活跃度')
        
        print(f"\n5. 前10名最活跃可转债 (按转股溢价率绝对值排序):")
        print("   代码      名称        债现价    转股溢价率    活跃度")
        print("   " + "-" * 50)
        for _, row in top_active.iterrows():
            print(f"   {row['债券代码']:<8} {row['债券简称']:<10} {row['债现价']:>8.2f} {row['转股溢价率']:>10.2f}% {row['活跃度']:>8.2f}%")

def test_data_processing():
    """测试数据处理流程"""
    print("\n" + "=" * 60)
    print("数据处理流程测试 / Data Processing Pipeline Test")
    print("=" * 60)
    
    try:
        # 获取数据
        print("1. 获取可转债数据...")
        data = ak.bond_zh_cov()
        print(f"   ✓ 获取到 {len(data)} 只可转债")
        
        # 数据清洗
        print("2. 数据清洗...")
        clean_data = data.dropna(subset=['债现价', '转股溢价率']).copy()
        print(f"   ✓ 清洗后剩余 {len(clean_data)} 只可转债")
        
        # 计算活跃度指标
        print("3. 计算活跃度指标...")
        clean_data['活跃度指标'] = abs(clean_data['转股溢价率'])
        print(f"   ✓ 活跃度指标范围: {clean_data['活跃度指标'].min():.2f}% ~ {clean_data['活跃度指标'].max():.2f}%")
        
        # 筛选前N名
        top_n = 20
        print(f"4. 筛选前{top_n}名最活跃可转债...")
        top_bonds = clean_data.nlargest(top_n, '活跃度指标')
        print(f"   ✓ 成功筛选出前{len(top_bonds)}名")
        
        # 显示结果
        print(f"\n前{top_n}名最活跃可转债:")
        print("排名  代码      名称        债现价    正股价    转股溢价率    活跃度")
        print("-" * 70)
        for i, (_, row) in enumerate(top_bonds.iterrows(), 1):
            print(f"{i:>2}   {row['债券代码']:<8} {row['债券简称']:<10} {row['债现价']:>8.2f} {row['正股价']:>8.2f} {row['转股溢价率']:>10.2f}% {row['活跃度指标']:>8.2f}%")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 处理失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试新的可转债API...")
    
    # 测试API对比
    test_old_vs_new_api()
    
    # 测试数据处理
    success = test_data_processing()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ 所有测试通过！新API可以成功替代旧API，并提供更多数据。")
    else:
        print("✗ 测试失败，需要进一步调试。")
    print("=" * 60)
