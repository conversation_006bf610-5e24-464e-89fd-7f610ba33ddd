#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可转债振幅分析系统 - 基于历史数据
Convertible Bond Amplitude Analysis System - Based on Historical Data

根据知乎文章方法，获取所有可转债2025年6月行情，按振幅排序
"""

import akshare as ak
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('convertible_bond_amplitude.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class BondAmplitudeResult:
    """可转债振幅分析结果"""
    bond_code: str
    bond_name: str
    period_amplitude: float  # 指定期间振幅
    daily_avg_amplitude: float  # 日均振幅
    period_max_price: float  # 指定期间最高价
    period_min_price: float  # 指定期间最低价
    period_avg_price: float  # 指定期间平均价
    period_trading_days: int  # 指定期间交易天数
    period_total_volume: float  # 指定期间总成交量
    volatility: float  # 波动率
    analysis_period: str  # 分析期间描述
    
class ConvertibleBondAmplitudeAnalyzer:
    """可转债振幅分析器"""

    def __init__(self, year: int = 2025, month: int = 6):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.bond_list = None
        self.results = []
        self.year = year
        self.month = month
        self.analysis_period = f"{year}年{month}月"
        
    def get_all_convertible_bonds(self) -> pd.DataFrame:
        """获取所有可转债列表"""
        try:
            self.logger.info("获取所有可转债列表...")
            bond_list = ak.bond_zh_cov()
            self.logger.info(f"获取到 {len(bond_list)} 只可转债")

            # 筛选已上市的可转债
            bond_list['上市时间'] = pd.to_datetime(bond_list['上市时间'], errors='coerce')
            listed_bonds = bond_list.dropna(subset=['上市时间']).copy()

            # 筛选指定分析期间之前上市的可转债
            analysis_date = datetime(self.year, self.month, 1)
            early_listed = listed_bonds[listed_bonds['上市时间'] < analysis_date].copy()

            self.logger.info(f"{self.analysis_period}前上市的可转债: {len(early_listed)} 只")
            self.bond_list = early_listed
            return early_listed
            
        except Exception as e:
            self.logger.error(f"获取可转债列表失败: {e}")
            return pd.DataFrame()
    
    def get_bond_historical_data(self, bond_code: str) -> Optional[pd.DataFrame]:
        """获取单只可转债历史数据"""
        try:
            # 根据交易所添加前缀
            if bond_code.startswith('11') or bond_code.startswith('12'):
                # 上交所可转债
                symbol = f"sh{bond_code}"
            elif bond_code.startswith('13'):
                # 深交所可转债
                symbol = f"sz{bond_code}"
            else:
                # 尝试不同前缀
                symbol = f"sh{bond_code}"

            self.logger.debug(f"获取 {symbol} 历史数据...")
            hist_data = ak.bond_zh_hs_cov_daily(symbol=symbol)

            if hist_data.empty:
                # 如果上交所失败，尝试深交所
                if symbol.startswith('sh'):
                    symbol = f"sz{bond_code}"
                    self.logger.debug(f"重试获取 {symbol} 历史数据...")
                    hist_data = ak.bond_zh_hs_cov_daily(symbol=symbol)

            if not hist_data.empty and 'date' in hist_data.columns:
                # 处理日期格式
                hist_data['date'] = pd.to_datetime(hist_data['date'])
                hist_data.set_index('date', inplace=True)
                # 重命名列为中文（保持一致性）
                hist_data.rename(columns={
                    'open': '开盘',
                    'high': '最高',
                    'low': '最低',
                    'close': '收盘',
                    'volume': '成交量'
                }, inplace=True)
                return hist_data

            return None

        except Exception as e:
            # 尝试另一个交易所
            try:
                if bond_code.startswith('11') or bond_code.startswith('12'):
                    symbol = f"sz{bond_code}"
                else:
                    symbol = f"sh{bond_code}"

                self.logger.debug(f"重试获取 {symbol} 历史数据...")
                hist_data = ak.bond_zh_hs_cov_daily(symbol=symbol)

                if not hist_data.empty and 'date' in hist_data.columns:
                    hist_data['date'] = pd.to_datetime(hist_data['date'])
                    hist_data.set_index('date', inplace=True)
                    hist_data.rename(columns={
                        'open': '开盘',
                        'high': '最高',
                        'low': '最低',
                        'close': '收盘',
                        'volume': '成交量'
                    }, inplace=True)
                    return hist_data

            except Exception as e2:
                self.logger.debug(f"获取 {bond_code} 历史数据完全失败: {e2}")

            return None
    
    def calculate_period_amplitude(self, bond_code: str, bond_name: str) -> Optional[BondAmplitudeResult]:
        """计算指定期间振幅"""
        try:
            # 获取历史数据
            hist_data = self.get_bond_historical_data(bond_code)
            if hist_data is None:
                return None

            # 计算指定月份的开始和结束日期
            from calendar import monthrange
            start_date = f"{self.year}-{self.month:02d}-01"
            _, last_day = monthrange(self.year, self.month)
            end_date = f"{self.year}-{self.month:02d}-{last_day}"

            # 筛选指定期间数据
            period_data = hist_data[(hist_data.index >= start_date) & (hist_data.index <= end_date)]

            if period_data.empty:
                self.logger.debug(f"{bond_code} 没有{self.analysis_period}数据")
                return None
            
            # 计算振幅指标
            period_max = period_data['最高'].max()
            period_min = period_data['最低'].min()
            period_avg = period_data['收盘'].mean()
            period_start = period_data['收盘'].iloc[0]

            # 月度振幅 = (最高价 - 最低价) / 期初价格 * 100%
            amplitude = ((period_max - period_min) / period_start * 100) if period_start > 0 else 0

            # 计算日均振幅
            daily_amplitudes = []
            for _, row in period_data.iterrows():
                daily_high = row['最高']
                daily_low = row['最低']
                daily_open = row['开盘']

                # 日振幅 = (当日最高价 - 当日最低价) / 当日开盘价 * 100%
                if daily_open > 0:
                    daily_amp = ((daily_high - daily_low) / daily_open * 100)
                    daily_amplitudes.append(daily_amp)

            # 日均振幅
            daily_avg_amplitude = sum(daily_amplitudes) / len(daily_amplitudes) if daily_amplitudes else 0

            # 波动率 = 收盘价标准差 / 平均价格 * 100%
            volatility = (period_data['收盘'].std() / period_avg * 100) if period_avg > 0 else 0

            # 总成交量
            total_volume = period_data['成交量'].sum() if '成交量' in period_data.columns else 0

            return BondAmplitudeResult(
                bond_code=bond_code,
                bond_name=bond_name,
                period_amplitude=amplitude,
                daily_avg_amplitude=daily_avg_amplitude,
                period_max_price=period_max,
                period_min_price=period_min,
                period_avg_price=period_avg,
                period_trading_days=len(period_data),
                period_total_volume=total_volume,
                volatility=volatility,
                analysis_period=self.analysis_period
            )
            
        except Exception as e:
            self.logger.debug(f"计算 {bond_code} 振幅失败: {e}")
            return None
    
    def analyze_all_bonds(self, max_bonds: int = None) -> List[BondAmplitudeResult]:
        """分析所有可转债的指定期间振幅"""
        if self.bond_list is None:
            self.get_all_convertible_bonds()

        if self.bond_list.empty:
            self.logger.error("没有可转债数据")
            return []

        # 限制分析数量
        bonds_to_analyze = self.bond_list.head(max_bonds) if max_bonds else self.bond_list
        total_bonds = len(bonds_to_analyze)

        self.logger.info(f"开始分析 {total_bonds} 只可转债的{self.analysis_period}振幅...")

        results = []
        success_count = 0

        for idx, (_, bond) in enumerate(bonds_to_analyze.iterrows(), 1):
            bond_code = bond['债券代码']
            bond_name = bond['债券简称']

            if idx % 10 == 0:
                self.logger.info(f"进度: {idx}/{total_bonds} ({idx/total_bonds*100:.1f}%)")

            result = self.calculate_period_amplitude(bond_code, bond_name)
            if result and result.daily_avg_amplitude > 0:
                results.append(result)
                success_count += 1

            # 避免请求过快
            time.sleep(0.1)

        self.logger.info(f"分析完成: {success_count}/{total_bonds} 只可转债有有效数据")
        self.results = results
        return results
    
    def get_top_amplitude_bonds(self, top_n: int = 20) -> List[BondAmplitudeResult]:
        """获取振幅前N名的可转债"""
        if not self.results:
            self.logger.warning("没有分析结果，请先运行 analyze_all_bonds()")
            return []
        
        # 按日均振幅排序
        sorted_results = sorted(self.results, key=lambda x: x.daily_avg_amplitude, reverse=True)
        return sorted_results[:top_n]
    
    def save_results(self, filename: str = None):
        """保存分析结果"""
        if not self.results:
            self.logger.warning("没有分析结果可保存")
            return
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"convertible_bond_amplitude_analysis_{self.year}{self.month:02d}_{timestamp}.csv"

        # 转换为DataFrame
        data = []
        for result in self.results:
            data.append({
                '可转债代码': result.bond_code,
                '可转债名称': result.bond_name,
                f'{self.analysis_period}振幅(%)': round(result.period_amplitude, 2),
                f'{self.analysis_period}日均振幅(%)': round(result.daily_avg_amplitude, 2),
                f'{self.analysis_period}最高价': round(result.period_max_price, 2),
                f'{self.analysis_period}最低价': round(result.period_min_price, 2),
                f'{self.analysis_period}平均价': round(result.period_avg_price, 2),
                f'{self.analysis_period}交易天数': result.period_trading_days,
                f'{self.analysis_period}总成交量': round(result.period_total_volume, 0),
                '波动率(%)': round(result.volatility, 2),
                '分析期间': result.analysis_period
            })

        df = pd.DataFrame(data)
        df = df.sort_values(f'{self.analysis_period}日均振幅(%)', ascending=False)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        
        self.logger.info(f"结果已保存到: {filename}")
        return filename
    
    def print_top_results(self, top_n: int = 20):
        """打印前N名结果"""
        top_bonds = self.get_top_amplitude_bonds(top_n)
        
        if not top_bonds:
            self.logger.warning("没有结果可显示")
            return
        
        print(f"\n{self.analysis_period}可转债日均振幅前{top_n}名:")
        print("=" * 120)
        print(f"{'排名':<4} {'代码':<8} {'名称':<12} {'日均振幅(%)':<10} {'月振幅(%)':<10} {'最高价':<8} {'最低价':<8} {'平均价':<8} {'交易天数':<8} {'波动率(%)':<10}")
        print("-" * 120)

        for i, result in enumerate(top_bonds, 1):
            print(f"{i:<4} {result.bond_code:<8} {result.bond_name:<12} "
                  f"{result.daily_avg_amplitude:<10.2f} {result.period_amplitude:<10.2f} "
                  f"{result.period_max_price:<8.2f} {result.period_min_price:<8.2f} "
                  f"{result.period_avg_price:<8.2f} {result.period_trading_days:<8} {result.volatility:<10.2f}")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="可转债振幅分析系统 - 支持自定义年份和月份")
    parser.add_argument('--year', type=int, default=2025, help='分析年份 (默认: 2025)')
    parser.add_argument('--month', type=int, default=6, help='分析月份 (默认: 6)')
    parser.add_argument('--max-bonds', type=int, default=50, help='最大分析可转债数量 (默认: 50)')
    parser.add_argument('--top-n', type=int, default=20, help='显示前N名结果 (默认: 20)')
    parser.add_argument('--output', type=str, help='输出文件名 (可选)')

    args = parser.parse_args()

    # 验证月份参数
    if not (1 <= args.month <= 12):
        print("错误: 月份必须在1-12之间")
        return

    # 验证年份参数
    if not (2000 <= args.year <= 2030):
        print("错误: 年份必须在2000-2030之间")
        return

    print(f"开始分析 {args.year}年{args.month}月 可转债振幅...")

    # 创建分析器
    analyzer = ConvertibleBondAmplitudeAnalyzer(year=args.year, month=args.month)

    # 分析所有可转债
    results = analyzer.analyze_all_bonds(max_bonds=args.max_bonds)

    if results:
        # 显示结果
        analyzer.print_top_results(top_n=args.top_n)

        # 保存结果
        filename = analyzer.save_results(args.output)

        print(f"\n分析完成!")
        print(f"- 分析期间: {args.year}年{args.month}月")
        print(f"- 总分析数量: {len(results)} 只可转债")
        print(f"- 结果文件: {filename}")
    else:
        print(f"分析失败，没有获取到{args.year}年{args.month}月的有效数据")

if __name__ == "__main__":
    main()
