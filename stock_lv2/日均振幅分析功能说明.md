# 可转债日均振幅分析功能说明

## 🎯 功能升级概述

根据您的要求，我已经成功将 `analyze_all_bonds` 方法改成计算**日均振幅**，现在系统提供更精确的日内波动分析。

## 📊 新增功能特性

### 1. 双重振幅指标
- **日均振幅** (主要指标): 反映平均每日的价格波动幅度
- **月度振幅** (参考指标): 反映整个月的价格波动范围

### 2. 智能排序
- 系统现在按**日均振幅**进行排序
- 更能体现可转债的日常交易活跃度

### 3. 增强的数据输出
- 控制台显示同时包含日均振幅和月度振幅
- CSV文件包含完整的双重振幅数据

## 🧮 计算公式详解

### 日均振幅计算
```
步骤1: 计算每日振幅
日振幅 = (当日最高价 - 当日最低价) / 当日开盘价 × 100%

步骤2: 计算平均值
日均振幅 = Σ(每日振幅) / 交易天数
```

### 月度振幅计算
```
月度振幅 = (月最高价 - 月最低价) / 月期初价格 × 100%
```

## 📈 实际分析结果对比

### 2024年12月前5名可转债
| 排名 | 代码   | 名称     | 日均振幅(%) | 月振幅(%) | 分析说明 |
|------|--------|----------|-------------|-----------|----------|
| 1    | 123244 | 松原转债 | 11.35       | 59.53     | 日均波动极高，月度涨幅巨大 |
| 2    | 123241 | 欧通转债 | 7.77        | 50.72     | 持续高波动，月度表现强劲 |
| 3    | 123246 | 远信转债 | 5.84        | 28.74     | 稳定的高日均波动 |
| 4    | 113684 | 湘泵转债 | 5.25        | 24.31     | 日内活跃度较高 |
| 5    | 113686 | 泰瑞转债 | 5.16        | 46.81     | 月度振幅高但日均相对稳定 |

### 2025年7月前5名可转债
| 排名 | 代码   | 名称     | 日均振幅(%) | 月振幅(%) | 分析说明 |
|------|--------|----------|-------------|-----------|----------|
| 1    | 118051 | 皓元转债 | 4.04        | 20.24     | 日内波动活跃 |
| 2    | 123251 | 华医转债 | 3.36        | 32.47     | 月度振幅大于日均振幅比例 |
| 3    | 123256 | 恒帅转债 | 2.61        | 11.37     | 相对稳定的波动模式 |
| 4    | 118054 | 安集转债 | 2.56        | 13.76     | 适中的日内活跃度 |
| 5    | 118053 | 正帆转债 | 2.10        | 8.60      | 低风险稳定波动 |

## 🔍 指标分析价值

### 日均振幅的优势
1. **更精确的活跃度衡量**: 反映每日平均的价格波动情况
2. **短线交易参考**: 适合日内交易者判断标的活跃程度
3. **风险评估**: 更准确地评估日常持仓风险
4. **策略制定**: 帮助制定合适的止损和止盈策略

### 月度振幅的参考价值
1. **趋势判断**: 反映整体价格走势的波动范围
2. **极值分析**: 显示价格的最大波动潜力
3. **长期投资**: 适合中长期投资者的风险评估

## 🚀 使用建议

### 1. 高日均振幅策略 (>5%)
```bash
# 寻找高活跃度可转债
python convertible_bond_amplitude_analyzer.py --year 2024 --month 12 --max-bonds 100 --top-n 20
```
- **适合**: 短线交易、日内波段操作
- **风险**: 高，需要严格止损
- **收益**: 潜在高收益

### 2. 中等日均振幅策略 (2-5%)
```bash
# 寻找平衡型可转债
python convertible_bond_amplitude_analyzer.py --year 2025 --month 7 --max-bonds 200 --top-n 30
```
- **适合**: 短中期交易、波段操作
- **风险**: 中等，相对可控
- **收益**: 稳定收益

### 3. 低日均振幅策略 (<2%)
- **适合**: 稳健投资、长期持有
- **风险**: 低，价格相对稳定
- **收益**: 稳定但有限

## 📋 输出文件说明

### CSV文件字段
- `可转债代码`: 6位数字代码
- `可转债名称`: 中文简称
- `{年月}振幅(%)`: 月度振幅
- `{年月}日均振幅(%)`: **主要指标** - 日均振幅
- `{年月}最高价`: 期间最高价格
- `{年月}最低价`: 期间最低价格
- `{年月}平均价`: 期间平均价格
- `{年月}交易天数`: 有效交易日数
- `{年月}总成交量`: 期间总成交量
- `波动率(%)`: 价格标准差/平均价格
- `分析期间`: 分析的具体年月

## 🎯 核心改进点

1. **主要排序指标**: 从月度振幅改为日均振幅
2. **数据结构增强**: 新增 `daily_avg_amplitude` 字段
3. **计算逻辑优化**: 遍历每日数据计算日振幅
4. **输出格式升级**: 同时显示两种振幅指标
5. **文件命名优化**: 包含年月信息的文件名

## 💡 使用技巧

1. **对比分析**: 关注日均振幅与月度振幅的比值关系
2. **时间序列**: 分析不同月份的日均振幅变化趋势
3. **筛选策略**: 根据投资风格选择合适的日均振幅范围
4. **组合配置**: 结合不同振幅水平的可转债构建投资组合

---

**更新时间**: 2025年8月2日  
**版本**: v2.0 - 日均振幅分析版  
**主要改进**: 新增日均振幅计算和排序功能
