"""
可转债分析系统测试脚本
"""

import sys
import logging
from datetime import datetime

# 导入可转债分析系统
from convertible_bond_analyzer import (
    可转债分析系统, 可转债配置, 可转债数据获取器, 
    可转债数据验证器, 可转债分析器
)
from config import StockConfig


def 测试配置系统():
    """测试配置系统"""
    print("测试可转债配置系统...")
    
    try:
        # 测试默认配置
        配置 = 可转债配置()
        print("✓ 默认可转债配置创建成功")
        
        # 验证配置属性
        assert 配置.最小交易天数 >= 1, "最小交易天数应该至少为1"
        assert 配置.前N名数量 >= 1, "前N名数量应该至少为1"
        assert len(配置.必需列名) > 0, "必需列名不能为空"
        print("✓ 配置属性验证通过")
        
        return True
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False


def 测试系统初始化():
    """测试系统初始化"""
    print("\n测试可转债分析系统初始化...")
    
    try:
        系统 = 可转债分析系统()
        print("✓ 系统初始化成功")
        
        # 测试组件初始化
        assert 系统.数据获取器 is not None, "数据获取器未初始化"
        assert 系统.数据验证器 is not None, "数据验证器未初始化"
        assert 系统.分析器 is not None, "分析器未初始化"
        print("✓ 所有组件初始化成功")
        
        return True
    except Exception as e:
        print(f"✗ 系统初始化失败: {e}")
        return False


def 测试数据获取器():
    """测试数据获取器功能"""
    print("\n测试可转债数据获取器...")
    
    try:
        获取器 = 可转债数据获取器()
        
        # 测试获取可转债列表
        print("测试获取可转债列表...")
        列表结果 = 获取器.获取可转债列表()
        
        if 列表结果.success:
            print(f"✓ 成功获取可转债列表，包含 {len(列表结果.data)} 只可转债")
            return True
        else:
            print(f"✗ 获取可转债列表失败: {列表结果.error}")
            # 这可能是由于网络问题或API变化，不一定是代码问题
            print("  注意: 这可能是由于网络问题或API接口变化导致的")
            return False
            
    except Exception as e:
        print(f"✗ 数据获取器测试失败: {e}")
        return False


def 测试数据验证器():
    """测试数据验证器功能"""
    print("\n测试可转债数据验证器...")
    
    try:
        import pandas as pd
        import numpy as np
        
        配置 = 可转债配置()
        验证器 = 可转债数据验证器(配置)
        
        # 创建测试数据
        测试数据 = pd.DataFrame({
            '最高价': [110.5, 112.3, 108.9, 115.2, 113.1],
            '最低价': [108.2, 109.1, 106.5, 112.8, 110.9],
            '开盘价': [109.0, 111.5, 107.2, 114.0, 112.5],
            '收盘价': [110.0, 111.8, 107.8, 114.5, 112.0],
            '成交量': [1000, 1200, 800, 1500, 1100]
        })
        
        # 测试验证功能
        验证结果 = 验证器.验证可转债数据(测试数据, "测试可转债")
        
        if 验证结果.is_valid:
            print("✓ 数据验证通过")
            print(f"  清洗后数据行数: {len(验证结果.cleaned_data)}")
            if 验证结果.stats and '数据质量评分' in 验证结果.stats:
                print(f"  数据质量评分: {验证结果.stats['数据质量评分']:.2f}")
            return True
        else:
            print(f"✗ 数据验证失败: {验证结果.errors}")
            return False
            
    except Exception as e:
        print(f"✗ 数据验证器测试失败: {e}")
        return False


def 测试分析器():
    """测试可转债分析器功能"""
    print("\n测试可转债分析器...")
    
    try:
        import pandas as pd
        
        配置 = 可转债配置()
        分析器 = 可转债分析器(配置)
        
        # 创建测试数据
        测试数据 = pd.DataFrame({
            '最高价': [110.5, 112.3, 108.9, 115.2, 113.1, 116.8, 114.5],
            '最低价': [108.2, 109.1, 106.5, 112.8, 110.9, 113.2, 111.8],
            '开盘价': [109.0, 111.5, 107.2, 114.0, 112.5, 115.0, 113.2],
            '收盘价': [110.0, 111.8, 107.8, 114.5, 112.0, 115.5, 113.0]
        })
        
        # 测试振幅计算
        振幅 = 分析器.计算振幅(测试数据, "测试可转债")
        if 振幅 is not None:
            print(f"✓ 振幅计算成功: {振幅:.2f}%")
        else:
            print("✗ 振幅计算失败")
            return False
        
        # 测试波动率计算
        波动率 = 分析器.计算波动率(测试数据, "测试可转债")
        if 波动率 is not None:
            print(f"✓ 波动率计算成功: {波动率:.2f}%")
        else:
            print("✗ 波动率计算失败")
            return False
        
        # 测试综合分析
        分析结果 = 分析器.分析可转债(测试数据, "TEST001", "测试可转债", 0.85)
        if 分析结果:
            print(f"✓ 综合分析成功")
            print(f"  可转债代码: {分析结果.可转债代码}")
            print(f"  日均振幅: {分析结果.日均振幅百分比:.2f}%")
            print(f"  交易天数: {分析结果.交易天数}")
            return True
        else:
            print("✗ 综合分析失败")
            return False
            
    except Exception as e:
        print(f"✗ 分析器测试失败: {e}")
        return False


def 测试会话统计():
    """测试会话统计功能"""
    print("\n测试会话统计...")
    
    try:
        系统 = 可转债分析系统()
        
        # 获取会话摘要
        摘要 = 系统.获取会话摘要()
        
        assert '会话持续时间' in 摘要, "缺少会话持续时间"
        assert '会话统计' in 摘要, "缺少会话统计"
        assert '获取器统计' in 摘要, "缺少获取器统计"
        assert '配置摘要' in 摘要, "缺少配置摘要"
        
        print("✓ 会话统计功能正常")
        return True
        
    except Exception as e:
        print(f"✗ 会话统计测试失败: {e}")
        return False


def 测试数据获取功能():
    """测试实际数据获取功能（可选）"""
    print("\n测试实际数据获取功能...")
    
    try:
        系统 = 可转债分析系统()
        
        # 运行数据获取测试
        测试成功 = 系统.测试数据获取(年份=2024, 月份=12)
        
        if 测试成功:
            print("✓ 实际数据获取测试通过")
            return True
        else:
            print("✗ 实际数据获取测试失败")
            print("  注意: 这可能是由于网络问题或API接口变化导致的")
            return False
            
    except Exception as e:
        print(f"✗ 实际数据获取测试失败: {e}")
        print("  注意: 这可能是由于网络问题或API接口变化导致的")
        return False


def main():
    """运行所有测试"""
    print("可转债分析系统 - 测试套件")
    print("=" * 60)
    
    # 设置日志级别以减少测试期间的噪音
    logging.basicConfig(level=logging.WARNING)
    
    测试函数列表 = [
        测试配置系统,
        测试系统初始化,
        测试数据验证器,
        测试分析器,
        测试会话统计,
        测试数据获取器,  # 这个可能因为网络问题失败
        # 测试数据获取功能,  # 注释掉，因为需要实际API访问
    ]
    
    通过数量 = 0
    总数量 = len(测试函数列表)
    
    for 测试函数 in 测试函数列表:
        try:
            if 测试函数():
                通过数量 += 1
        except Exception as e:
            print(f"✗ 测试 {测试函数.__name__} 发生异常: {e}")
    
    print(f"\n测试结果: {通过数量}/{总数量} 测试通过")
    
    if 通过数量 == 总数量:
        print("🎉 所有测试通过！可转债分析系统已准备就绪。")
        return 0
    elif 通过数量 >= 总数量 - 1:  # 允许一个测试失败（通常是网络相关的）
        print("✅ 核心功能测试通过！系统基本可用。")
        print("   注意: 某些测试可能因网络问题或API变化而失败。")
        return 0
    else:
        print("❌ 多个测试失败。请检查配置和依赖项。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
