"""
Test script for the convertible bond analysis system (English version)
"""

import sys
import logging
from datetime import datetime

# Import convertible bond analysis system
from convertible_bond_analyzer_en import (
    ConvertibleBondAnalysisSystem, ConvertibleBondConfig, ConvertibleBondDataFetcher, 
    ConvertibleBondDataValidator, ConvertibleBondAnalyzer
)
from config import StockConfig


def test_configuration_system():
    """Test configuration system"""
    print("Testing convertible bond configuration system...")
    
    try:
        # Test default configuration
        config = ConvertibleBondConfig()
        print("✓ Default convertible bond configuration created successfully")
        
        # Validate configuration attributes
        assert config.min_data_requirement >= 1, "Minimum data requirement should be at least 1"
        assert config.top_n_count >= 1, "Top N count should be at least 1"
        assert len(config.required_columns) > 0, "Required columns cannot be empty"
        print("✓ Configuration attribute validation passed")
        
        return True
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


def test_system_initialization():
    """Test system initialization"""
    print("\nTesting convertible bond analysis system initialization...")
    
    try:
        system = ConvertibleBondAnalysisSystem()
        print("✓ System initialized successfully")
        
        # Test component initialization
        assert system.data_fetcher is not None, "Data fetcher not initialized"
        assert system.data_validator is not None, "Data validator not initialized"
        assert system.analyzer is not None, "Analyzer not initialized"
        print("✓ All components initialized successfully")
        
        return True
    except Exception as e:
        print(f"✗ System initialization failed: {e}")
        return False


def test_data_fetcher():
    """Test data fetcher functionality"""
    print("\nTesting convertible bond data fetcher...")
    
    try:
        fetcher = ConvertibleBondDataFetcher()
        
        # Test getting convertible bond list
        print("Testing convertible bond list retrieval...")
        list_result = fetcher.get_convertible_bond_current_data()
        
        if list_result.success:
            print(f"✓ Successfully retrieved convertible bond market data with {len(list_result.data)} bonds")
            return True
        else:
            print(f"✗ Failed to retrieve convertible bond market data: {list_result.error}")
            # This might be due to network issues or API changes, not necessarily code issues
            print("  Note: This might be due to network issues or API interface changes")
            return False
            
    except Exception as e:
        print(f"✗ Data fetcher test failed: {e}")
        return False


def test_data_validator():
    """Test data validator functionality"""
    print("\nTesting convertible bond data validator...")
    
    try:
        import pandas as pd
        import numpy as np
        
        config = ConvertibleBondConfig()
        validator = ConvertibleBondDataValidator(config)
        
        # Create test data
        test_data = pd.DataFrame({
            '现价': [110.5],
            '涨跌幅': [2.5],
            '转股溢价率': [15.2],
            '到期税前收益': [3.8],
            '成交额': [1000000]
        })
        
        # Test validation functionality
        validation_result = validator.validate_convertible_bond_data(test_data, "TEST_BOND")
        
        if validation_result.is_valid:
            print("✓ Data validation passed")
            print(f"  Cleaned data rows: {len(validation_result.cleaned_data)}")
            if validation_result.stats and 'data_quality_score' in validation_result.stats:
                print(f"  Data quality score: {validation_result.stats['data_quality_score']:.2f}")
            return True
        else:
            print(f"✗ Data validation failed: {validation_result.errors}")
            return False
            
    except Exception as e:
        print(f"✗ Data validator test failed: {e}")
        return False


def test_analyzer():
    """Test convertible bond analyzer functionality"""
    print("\nTesting convertible bond analyzer...")
    
    try:
        import pandas as pd
        
        config = ConvertibleBondConfig()
        analyzer = ConvertibleBondAnalyzer(config)
        
        # Create test data
        test_data = pd.DataFrame({
            '现价': [110.5],
            '涨跌幅': [3.2],
            '转股溢价率': [12.5],
            '到期税前收益': [4.1]
        })
        
        # Test performance indicator calculation
        performance_indicator = analyzer.calculate_daily_performance_indicator(test_data, "TEST_BOND")
        if performance_indicator is not None:
            print(f"✓ Performance indicator calculation successful: {performance_indicator:.2f}%")
        else:
            print("✗ Performance indicator calculation failed")
            return False
        
        # Test conversion premium calculation
        conversion_premium = analyzer.calculate_conversion_premium_rate(test_data, "TEST_BOND")
        if conversion_premium is not None:
            print(f"✓ Conversion premium calculation successful: {conversion_premium:.2f}%")
        else:
            print("✗ Conversion premium calculation failed")
            return False
        
        # Test comprehensive analysis
        analysis_result = analyzer.analyze_convertible_bond(test_data, "TEST001", "Test Bond", 0.85)
        if analysis_result:
            print(f"✓ Comprehensive analysis successful")
            print(f"  Bond Code: {analysis_result.bond_code}")
            print(f"  Daily Amplitude: {analysis_result.daily_amplitude_pct:.2f}%")
            print(f"  Trading Days: {analysis_result.trading_days}")
            return True
        else:
            print("✗ Comprehensive analysis failed")
            return False
            
    except Exception as e:
        print(f"✗ Analyzer test failed: {e}")
        return False


def test_session_statistics():
    """Test session statistics functionality"""
    print("\nTesting session statistics...")
    
    try:
        system = ConvertibleBondAnalysisSystem()
        
        # Get session summary
        summary = system.get_session_summary()
        
        assert 'session_duration' in summary, "Missing session duration"
        assert 'session_stats' in summary, "Missing session stats"
        assert 'fetcher_stats' in summary, "Missing fetcher stats"
        assert 'config_summary' in summary, "Missing config summary"
        
        print("✓ Session statistics functionality working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Session statistics test failed: {e}")
        return False


def test_actual_data_retrieval():
    """Test actual data retrieval functionality (optional)"""
    print("\nTesting actual data retrieval functionality...")
    
    try:
        system = ConvertibleBondAnalysisSystem()
        
        # Run data retrieval test
        test_success = system.test_data_retrieval(year=2024, month=12)
        
        if test_success:
            print("✓ Actual data retrieval test passed")
            return True
        else:
            print("✗ Actual data retrieval test failed")
            print("  Note: This might be due to network issues or API interface changes")
            return False
            
    except Exception as e:
        print(f"✗ Actual data retrieval test failed: {e}")
        print("  Note: This might be due to network issues or API interface changes")
        return False


def main():
    """Run all tests"""
    print("Convertible Bond Analysis System - Test Suite (English Version)")
    print("=" * 70)
    
    # Set logging level to reduce noise during testing
    logging.basicConfig(level=logging.WARNING)
    
    test_functions = [
        test_configuration_system,
        test_system_initialization,
        test_data_validator,
        test_analyzer,
        test_session_statistics,
        test_data_fetcher,  # This might fail due to network issues
        # test_actual_data_retrieval,  # Commented out as it requires actual API access
    ]
    
    passed = 0
    total = len(test_functions)
    
    for test_func in test_functions:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test_func.__name__} failed with exception: {e}")
    
    print(f"\nTest Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The convertible bond analysis system is ready to use.")
        return 0
    elif passed >= total - 1:  # Allow one test to fail (usually network-related)
        print("✅ Core functionality tests passed! System is basically usable.")
        print("   Note: Some tests might fail due to network issues or API changes.")
        return 0
    else:
        print("❌ Multiple tests failed. Please check configuration and dependencies.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
