# 可转债振幅分析系统使用说明

## 功能简介

本系统基于akshare的`bond_zh_hs_cov_daily`API，可以获取所有可转债的历史行情数据，并计算**任意年份和月份**的价格振幅，按**日均振幅**排序。

## 主要特性

✅ **全面覆盖**: 支持上交所和深交所所有可转债
✅ **历史数据**: 获取完整的日线行情数据
✅ **灵活时间**: 支持自定义年份和月份分析
✅ **双重振幅**: 同时计算日均振幅和月度振幅
✅ **智能排序**: 按日均振幅大小自动排序
✅ **数据导出**: 支持CSV格式导出分析结果
✅ **进度显示**: 实时显示分析进度

## 快速开始

### 1. 基本用法 (默认2025年6月)
```bash
# 分析前100只可转债的2025年6月振幅，显示前20名
python convertible_bond_amplitude_analyzer.py --max-bonds 100 --top-n 20
```

### 2. 自定义年份和月份
```bash
# 分析2024年12月的可转债振幅
python convertible_bond_amplitude_analyzer.py --year 2024 --month 12 --max-bonds 50 --top-n 15

# 分析2025年7月的可转债振幅
python convertible_bond_amplitude_analyzer.py --year 2025 --month 7 --max-bonds 100 --top-n 20
```

### 3. 大规模分析
```bash
# 分析前500只可转债，显示前30名
python convertible_bond_amplitude_analyzer.py --year 2025 --month 6 --max-bonds 500 --top-n 30
```

### 4. 自定义输出文件
```bash
# 指定输出文件名
python convertible_bond_amplitude_analyzer.py --year 2024 --month 11 --max-bonds 200 --output 2024_11_analysis.csv
```

## 参数说明

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `--year` | 分析年份 | 2025 | `--year 2024` |
| `--month` | 分析月份 | 6 | `--month 12` |
| `--max-bonds` | 最大分析可转债数量 | 50 | `--max-bonds 500` |
| `--top-n` | 显示前N名结果 | 20 | `--top-n 30` |
| `--output` | 输出文件名 | 自动生成 | `--output result.csv` |

### 参数限制
- **年份**: 2000-2030之间
- **月份**: 1-12之间
- **最大分析数量**: 建议不超过1000只
- **显示数量**: 建议不超过100名

## 输出说明

### 控制台输出
```
2024年12月可转债日均振幅前15名:
========================================================================================================================
排名   代码       名称           日均振幅(%)    月振幅(%)     最高价      最低价      平均价      交易天数     波动率(%)
------------------------------------------------------------------------------------------------------------------------
1    123244   松原转债         11.35      59.53      278.00   173.52   205.15   22       11.06
2    123241   欧通转债         7.77       50.72      271.52   174.70   210.42   22       14.77
3    123246   远信转债         5.84       28.74      298.46   229.60   244.46   22       4.61
...
```

### CSV文件输出
包含以下字段：
- 可转债代码
- 可转债名称
- {年份}年{月份}振幅(%) (月度振幅)
- {年份}年{月份}日均振幅(%) (主要指标)
- {年份}年{月份}最高价
- {年份}年{月份}最低价
- {年份}年{月份}平均价
- {年份}年{月份}交易天数
- {年份}年{月份}总成交量
- 波动率(%)
- 分析期间

## 振幅计算公式

### 1. 日均振幅 (主要排序指标)
```
日振幅 = (当日最高价 - 当日最低价) / 当日开盘价 × 100%
日均振幅 = Σ(每日振幅) / 交易天数
```

### 2. 月度振幅 (参考指标)
```
月度振幅 = (月最高价 - 月最低价) / 月期初价格 × 100%
```

### 指标含义
- **日均振幅**: 反映平均每日的价格波动幅度，更能体现日常交易活跃度
- **月度振幅**: 反映整个月的价格波动范围，体现极值差异
- **振幅越大**: 价格波动越剧烈，投机性越强，适合短线交易
- **振幅越小**: 价格相对稳定，风险较低，适合稳健投资

## 实际运行示例

### 示例1: 分析2025年7月数据
```bash
$ python convertible_bond_amplitude_analyzer.py --year 2025 --month 7 --max-bonds 20 --top-n 10

2025年7月可转债日均振幅前10名:
========================================================================================================================
排名   代码       名称           日均振幅(%)    月振幅(%)     最高价      最低价      平均价      交易天数     波动率(%)
------------------------------------------------------------------------------------------------------------------------
1    118051   皓元转债         4.04       20.24      165.45   137.13   146.92   23       4.12
2    123251   华医转债         3.36       32.47      168.90   127.00   133.42   23       4.74
...
分析完成!
- 分析期间: 2025年7月
- 总分析数量: 19 只可转债
- 结果文件: convertible_bond_amplitude_analysis_202507_20250802_230433.csv
```

### 示例2: 分析2024年12月数据
```bash
$ python convertible_bond_amplitude_analyzer.py --year 2024 --month 12 --max-bonds 30 --top-n 15

分析完成!
- 分析期间: 2024年12月
- 总分析数量: 29 只可转债
- 结果文件: convertible_bond_amplitude_analysis_202412_20250802_230506.csv
```

## 数据质量说明

- **成功率**: 通常为60-70%（部分可转债可能没有历史数据）
- **数据完整性**: 每只成功分析的可转债都有完整的6月交易数据
- **交易天数**: 2025年6月通常有20个交易日
- **数据来源**: akshare官方API，数据质量可靠

## 注意事项

1. **网络要求**: 需要稳定的网络连接获取数据
2. **分析时间**: 大规模分析(500只)约需5-10分钟
3. **API限制**: 建议不要过于频繁调用，避免被限制
4. **数据时效**: 分析的是2025年6月的历史数据

## 故障排除

### 常见问题

**Q: 为什么有些可转债没有数据？**  
A: 部分可转债可能在6月份没有交易，或者API暂时无法获取数据。

**Q: 分析速度很慢怎么办？**  
A: 可以减少`--max-bonds`参数，先分析少量数据测试。

**Q: 输出文件在哪里？**  
A: 默认保存在当前目录，文件名包含时间戳。

### 错误处理

系统具有完善的错误处理机制：
- 自动重试不同交易所的数据
- 跳过无效数据，继续分析其他可转债
- 详细的日志记录，便于问题排查

## 扩展功能

可以根据需要修改代码实现：
- 分析不同时间段的振幅
- 添加更多技术指标
- 集成其他数据源
- 实现实时监控功能

---

**开发者**: 基于akshare API开发  
**更新时间**: 2025年8月2日  
**版本**: v1.0
