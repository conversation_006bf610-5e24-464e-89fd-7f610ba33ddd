#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可转债活跃度分析系统 - 基于当前市场数据
Convertible Bond Activity Analysis System - Based on Current Market Data

由于历史数据API限制，使用当前市场数据的多个指标来评估可转债活跃度
"""

import akshare as ak
import pandas as pd
import numpy as np
import time
from datetime import datetime
from typing import List, Dict, Optional
import logging
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class BondActivityResult:
    """可转债活跃度分析结果"""
    bond_code: str
    bond_name: str
    current_price: float  # 当前价格
    conversion_premium: float  # 转股溢价率
    conversion_value: float  # 转股价值
    stock_price: float  # 正股价格
    activity_score: float  # 综合活跃度评分
    price_deviation: float  # 价格偏离度
    risk_level: str  # 风险等级

class ConvertibleBondActivityAnalyzer:
    """可转债活跃度分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.bond_data = None
        self.results = []
        
    def get_all_convertible_bonds_data(self) -> pd.DataFrame:
        """获取所有可转债当前市场数据"""
        try:
            self.logger.info("获取所有可转债市场数据...")
            bond_data = ak.bond_zh_cov()
            self.logger.info(f"获取到 {len(bond_data)} 只可转债数据")
            
            # 数据清洗
            # 筛选有效数据
            valid_data = bond_data.dropna(subset=['债现价', '转股溢价率']).copy()
            self.logger.info(f"有效数据: {len(valid_data)} 只可转债")
            
            self.bond_data = valid_data
            return valid_data
            
        except Exception as e:
            self.logger.error(f"获取可转债数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_activity_score(self, row: pd.Series) -> BondActivityResult:
        """计算单只可转债的活跃度评分"""
        try:
            bond_code = row['债券代码']
            bond_name = row['债券简称']
            current_price = float(row['债现价']) if pd.notna(row['债现价']) else 0
            conversion_premium = float(row['转股溢价率']) if pd.notna(row['转股溢价率']) else 0
            conversion_value = float(row['转股价值']) if pd.notna(row['转股价值']) else 0
            stock_price = float(row['正股价']) if pd.notna(row['正股价']) else 0
            
            # 计算活跃度评分 (多因子模型)
            activity_score = 0
            
            # 1. 转股溢价率绝对值 (权重40%) - 溢价率越高，活跃度越高
            premium_score = min(abs(conversion_premium) / 100 * 40, 40)
            activity_score += premium_score
            
            # 2. 价格偏离度 (权重30%) - 债券价格与转股价值的偏离程度
            if conversion_value > 0:
                price_deviation = abs(current_price - conversion_value) / conversion_value * 100
                deviation_score = min(price_deviation / 50 * 30, 30)  # 最大50%偏离度
                activity_score += deviation_score
            else:
                price_deviation = 0
            
            # 3. 价格水平 (权重20%) - 价格越偏离100，活跃度越高
            price_level_score = min(abs(current_price - 100) / 100 * 20, 20)
            activity_score += price_level_score
            
            # 4. 转股价值合理性 (权重10%) - 转股价值与债券价格的比值
            if current_price > 0 and conversion_value > 0:
                value_ratio = conversion_value / current_price
                ratio_score = min(abs(1 - value_ratio) * 100 / 50 * 10, 10)
                activity_score += ratio_score
            
            # 风险等级评估
            if conversion_premium > 50:
                risk_level = "高风险"
            elif conversion_premium > 20:
                risk_level = "中风险"
            elif conversion_premium > -10:
                risk_level = "低风险"
            else:
                risk_level = "强制转股风险"
            
            return BondActivityResult(
                bond_code=bond_code,
                bond_name=bond_name,
                current_price=current_price,
                conversion_premium=conversion_premium,
                conversion_value=conversion_value,
                stock_price=stock_price,
                activity_score=activity_score,
                price_deviation=price_deviation,
                risk_level=risk_level
            )
            
        except Exception as e:
            self.logger.debug(f"计算 {bond_code} 活跃度失败: {e}")
            return None
    
    def analyze_all_bonds(self, max_bonds: int = None) -> List[BondActivityResult]:
        """分析所有可转债的活跃度"""
        if self.bond_data is None:
            self.get_all_convertible_bonds_data()
        
        if self.bond_data.empty:
            self.logger.error("没有可转债数据")
            return []
        
        # 限制分析数量
        bonds_to_analyze = self.bond_data.head(max_bonds) if max_bonds else self.bond_data
        total_bonds = len(bonds_to_analyze)
        
        self.logger.info(f"开始分析 {total_bonds} 只可转债的活跃度...")
        
        results = []
        success_count = 0
        
        for idx, (_, bond) in enumerate(bonds_to_analyze.iterrows(), 1):
            if idx % 50 == 0:
                self.logger.info(f"进度: {idx}/{total_bonds} ({idx/total_bonds*100:.1f}%)")
            
            result = self.calculate_activity_score(bond)
            if result and result.activity_score > 0:
                results.append(result)
                success_count += 1
        
        self.logger.info(f"分析完成: {success_count}/{total_bonds} 只可转债有有效数据")
        self.results = results
        return results
    
    def get_top_active_bonds(self, top_n: int = 20) -> List[BondActivityResult]:
        """获取活跃度前N名的可转债"""
        if not self.results:
            self.logger.warning("没有分析结果，请先运行 analyze_all_bonds()")
            return []
        
        # 按活跃度评分排序
        sorted_results = sorted(self.results, key=lambda x: x.activity_score, reverse=True)
        return sorted_results[:top_n]
    
    def get_high_premium_bonds(self, min_premium: float = 30, top_n: int = 20) -> List[BondActivityResult]:
        """获取高溢价率的可转债 (模拟高振幅)"""
        if not self.results:
            return []
        
        # 筛选高溢价率的可转债
        high_premium = [r for r in self.results if abs(r.conversion_premium) >= min_premium]
        # 按溢价率绝对值排序
        sorted_results = sorted(high_premium, key=lambda x: abs(x.conversion_premium), reverse=True)
        return sorted_results[:top_n]
    
    def save_results(self, filename: str = None):
        """保存分析结果"""
        if not self.results:
            self.logger.warning("没有分析结果可保存")
            return
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"convertible_bond_activity_analysis_{timestamp}.csv"
        
        # 转换为DataFrame
        data = []
        for result in self.results:
            data.append({
                '可转债代码': result.bond_code,
                '可转债名称': result.bond_name,
                '当前价格': round(result.current_price, 2),
                '转股溢价率(%)': round(result.conversion_premium, 2),
                '转股价值': round(result.conversion_value, 2),
                '正股价格': round(result.stock_price, 2),
                '活跃度评分': round(result.activity_score, 2),
                '价格偏离度(%)': round(result.price_deviation, 2),
                '风险等级': result.risk_level
            })
        
        df = pd.DataFrame(data)
        df = df.sort_values('活跃度评分', ascending=False)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        
        self.logger.info(f"结果已保存到: {filename}")
        return filename
    
    def print_top_results(self, top_n: int = 20):
        """打印前N名结果"""
        top_bonds = self.get_top_active_bonds(top_n)
        
        if not top_bonds:
            self.logger.warning("没有结果可显示")
            return
        
        print(f"\n可转债活跃度前{top_n}名 (基于多因子评分模型):")
        print("=" * 120)
        print(f"{'排名':<4} {'代码':<8} {'名称':<12} {'当前价格':<8} {'转股溢价率(%)':<12} {'转股价值':<8} {'活跃度评分':<10} {'风险等级':<10}")
        print("-" * 120)
        
        for i, result in enumerate(top_bonds, 1):
            print(f"{i:<4} {result.bond_code:<8} {result.bond_name:<12} "
                  f"{result.current_price:<8.2f} {result.conversion_premium:<12.2f} "
                  f"{result.conversion_value:<8.2f} {result.activity_score:<10.2f} {result.risk_level:<10}")
    
    def print_high_premium_results(self, min_premium: float = 30, top_n: int = 20):
        """打印高溢价率结果 (模拟高振幅)"""
        high_premium_bonds = self.get_high_premium_bonds(min_premium, top_n)
        
        if not high_premium_bonds:
            self.logger.warning(f"没有找到溢价率超过{min_premium}%的可转债")
            return
        
        print(f"\n高溢价率可转债前{top_n}名 (溢价率≥{min_premium}%, 模拟高振幅):")
        print("=" * 120)
        print(f"{'排名':<4} {'代码':<8} {'名称':<12} {'当前价格':<8} {'转股溢价率(%)':<12} {'转股价值':<8} {'价格偏离度(%)':<12} {'风险等级':<10}")
        print("-" * 120)
        
        for i, result in enumerate(high_premium_bonds, 1):
            print(f"{i:<4} {result.bond_code:<8} {result.bond_name:<12} "
                  f"{result.current_price:<8.2f} {result.conversion_premium:<12.2f} "
                  f"{result.conversion_value:<8.2f} {result.price_deviation:<12.2f} {result.risk_level:<10}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="可转债活跃度分析系统 (基于当前市场数据)")
    parser.add_argument('--max-bonds', type=int, default=500, help='最大分析可转债数量')
    parser.add_argument('--top-n', type=int, default=20, help='显示前N名结果')
    parser.add_argument('--min-premium', type=float, default=30, help='高溢价率阈值')
    parser.add_argument('--output', type=str, help='输出文件名')
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = ConvertibleBondActivityAnalyzer()
    
    # 分析所有可转债
    results = analyzer.analyze_all_bonds(max_bonds=args.max_bonds)
    
    if results:
        # 显示活跃度结果
        analyzer.print_top_results(top_n=args.top_n)
        
        # 显示高溢价率结果 (模拟高振幅)
        analyzer.print_high_premium_results(min_premium=args.min_premium, top_n=args.top_n)
        
        # 保存结果
        filename = analyzer.save_results(args.output)
        
        print(f"\n分析完成!")
        print(f"- 总分析数量: {len(results)} 只可转债")
        print(f"- 结果文件: {filename}")
        print(f"\n说明:")
        print(f"- 由于akshare历史数据API限制，使用当前市场数据的多因子模型评估活跃度")
        print(f"- 活跃度评分基于: 转股溢价率(40%) + 价格偏离度(30%) + 价格水平(20%) + 价值比率(10%)")
        print(f"- 高溢价率可转债通常对应高振幅特征")
    else:
        print("分析失败，没有获取到有效数据")

if __name__ == "__main__":
    main()
