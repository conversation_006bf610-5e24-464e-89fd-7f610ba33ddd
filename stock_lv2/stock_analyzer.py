"""
Stock analysis module for calculating amplitude and other metrics.
Inspired by Context7's modular and robust analysis approach.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
import logging
from datetime import datetime

from config import get_config, AnalysisConfig


@dataclass
class AnalysisResult:
    """Result of stock analysis with metrics and metadata."""
    stock_code: str
    stock_name: str
    avg_amplitude: float
    trading_days: int
    metrics: Dict[str, float]
    metadata: Dict[str, Any]


class StockAnalyzer:
    """
    Advanced stock analyzer for calculating amplitude and other technical indicators.
    """
    
    def __init__(self, config: Optional[AnalysisConfig] = None):
        """
        Initialize the stock analyzer.
        
        Args:
            config: Analysis configuration. If None, uses global config.
        """
        self.config = config or get_config().analysis
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def calculate_amplitude(self, data: pd.DataFrame, stock_code: str) -> Optional[float]:
        """
        Calculate the average daily amplitude for a stock.
        
        Args:
            data: Stock data DataFrame with 最高, 最低, 收盘 columns
            stock_code: Stock code for logging
            
        Returns:
            Average amplitude as percentage, or None if calculation fails
        """
        try:
            if data.empty:
                self.logger.warning(f"Stock {stock_code}: Empty data for amplitude calculation")
                return None
            
            required_cols = ['最高', '最低', '收盘']
            if not all(col in data.columns for col in required_cols):
                missing = [col for col in required_cols if col not in data.columns]
                self.logger.warning(f"Stock {stock_code}: Missing columns for amplitude: {missing}")
                return None
            
            # Calculate daily amplitude: (High - Low) / Previous Close
            # Use shift(1) to get previous day's closing price
            prev_close = data['收盘'].shift(1)
            daily_amplitude = (data['最高'] - data['最低']) / prev_close
            
            # Remove NaN values (first day will be NaN due to shift)
            valid_amplitude = daily_amplitude.dropna()
            
            if len(valid_amplitude) == 0:
                self.logger.warning(f"Stock {stock_code}: No valid amplitude data after calculation")
                return None
            
            # Calculate average and convert to percentage
            avg_amplitude = valid_amplitude.mean() * 100
            
            self.logger.debug(f"Stock {stock_code}: Calculated amplitude {avg_amplitude:.2f}% from {len(valid_amplitude)} days")
            return avg_amplitude
            
        except Exception as e:
            self.logger.error(f"Stock {stock_code}: Error calculating amplitude: {e}")
            return None
    
    def calculate_volatility(self, data: pd.DataFrame, stock_code: str) -> Optional[float]:
        """
        Calculate price volatility (standard deviation of returns).
        
        Args:
            data: Stock data DataFrame
            stock_code: Stock code for logging
            
        Returns:
            Volatility as percentage, or None if calculation fails
        """
        try:
            if '收盘' not in data.columns or len(data) < 2:
                return None
            
            # Calculate daily returns
            returns = data['收盘'].pct_change().dropna()
            
            if len(returns) == 0:
                return None
            
            # Calculate volatility (standard deviation of returns)
            volatility = returns.std() * 100
            return volatility
            
        except Exception as e:
            self.logger.error(f"Stock {stock_code}: Error calculating volatility: {e}")
            return None
    
    def calculate_price_range(self, data: pd.DataFrame, stock_code: str) -> Dict[str, float]:
        """
        Calculate price range statistics.
        
        Args:
            data: Stock data DataFrame
            stock_code: Stock code for logging
            
        Returns:
            Dictionary with price range statistics
        """
        try:
            stats = {}
            
            if '收盘' in data.columns:
                close_prices = data['收盘']
                stats.update({
                    'min_price': float(close_prices.min()),
                    'max_price': float(close_prices.max()),
                    'price_range': float(close_prices.max() - close_prices.min()),
                    'avg_price': float(close_prices.mean())
                })
            
            if all(col in data.columns for col in ['最高', '最低']):
                stats.update({
                    'highest_high': float(data['最高'].max()),
                    'lowest_low': float(data['最低'].min()),
                    'total_range': float(data['最高'].max() - data['最低'].min())
                })
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Stock {stock_code}: Error calculating price range: {e}")
            return {}
    
    def calculate_trading_metrics(self, data: pd.DataFrame, stock_code: str) -> Dict[str, float]:
        """
        Calculate various trading metrics.
        
        Args:
            data: Stock data DataFrame
            stock_code: Stock code for logging
            
        Returns:
            Dictionary with trading metrics
        """
        try:
            metrics = {}
            
            # Basic metrics
            metrics['trading_days'] = len(data)
            
            if '成交量' in data.columns:
                volume = data['成交量']
                metrics.update({
                    'avg_volume': float(volume.mean()),
                    'max_volume': float(volume.max()),
                    'min_volume': float(volume.min()),
                    'volume_std': float(volume.std())
                })
            
            # Price change metrics
            if '收盘' in data.columns and len(data) > 1:
                close_prices = data['收盘']
                first_price = close_prices.iloc[0]
                last_price = close_prices.iloc[-1]
                
                metrics.update({
                    'total_return': float((last_price - first_price) / first_price * 100),
                    'max_daily_change': float(close_prices.pct_change().abs().max() * 100)
                })
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Stock {stock_code}: Error calculating trading metrics: {e}")
            return {}
    
    def analyze_stock(self, data: pd.DataFrame, stock_code: str, stock_name: str) -> Optional[AnalysisResult]:
        """
        Perform comprehensive analysis of a single stock.
        
        Args:
            data: Stock data DataFrame
            stock_code: Stock code
            stock_name: Stock name
            
        Returns:
            AnalysisResult with all calculated metrics, or None if analysis fails
        """
        try:
            # Calculate amplitude
            avg_amplitude = self.calculate_amplitude(data, stock_code)
            if avg_amplitude is None:
                self.logger.warning(f"Stock {stock_code}: Failed to calculate amplitude")
                return None
            
            # Check minimum trading days requirement
            trading_days = len(data)
            if trading_days < self.config.min_trading_days:
                self.logger.debug(
                    f"Stock {stock_code}: Insufficient trading days "
                    f"({trading_days} < {self.config.min_trading_days})"
                )
                return None
            
            # Check amplitude threshold
            if avg_amplitude < self.config.amplitude_threshold:
                self.logger.debug(
                    f"Stock {stock_code}: Amplitude below threshold "
                    f"({avg_amplitude:.2f}% < {self.config.amplitude_threshold}%)"
                )
                return None
            
            # Calculate additional metrics
            volatility = self.calculate_volatility(data, stock_code)
            price_range = self.calculate_price_range(data, stock_code)
            trading_metrics = self.calculate_trading_metrics(data, stock_code)
            
            # Combine all metrics
            metrics = {
                'volatility': volatility or 0.0,
                **price_range,
                **trading_metrics
            }
            
            # Create metadata
            metadata = {
                'analysis_date': datetime.now().isoformat(),
                'data_start_date': str(data.index[0]) if not data.empty else None,
                'data_end_date': str(data.index[-1]) if not data.empty else None,
                'data_quality_score': self._calculate_data_quality_score(data)
            }
            
            return AnalysisResult(
                stock_code=stock_code,
                stock_name=stock_name,
                avg_amplitude=avg_amplitude,
                trading_days=trading_days,
                metrics=metrics,
                metadata=metadata
            )
            
        except Exception as e:
            self.logger.error(f"Stock {stock_code}: Analysis failed: {e}")
            return None
    
    def analyze_batch(self, stock_data_dict: Dict[str, pd.DataFrame], 
                     stock_names: Dict[str, str]) -> List[AnalysisResult]:
        """
        Analyze multiple stocks and return sorted results.
        
        Args:
            stock_data_dict: Dictionary mapping stock codes to DataFrames
            stock_names: Dictionary mapping stock codes to names
            
        Returns:
            List of AnalysisResults sorted by amplitude (descending)
        """
        results = []
        
        for stock_code, data in stock_data_dict.items():
            stock_name = stock_names.get(stock_code, f"股票{stock_code}")
            result = self.analyze_stock(data, stock_code, stock_name)
            if result:
                results.append(result)
        
        # Sort by amplitude (descending) and take top N
        results.sort(key=lambda x: x.avg_amplitude, reverse=True)
        top_results = results[:self.config.top_n_stocks]
        
        self.logger.info(
            f"Analysis completed: {len(top_results)} stocks selected from {len(results)} valid stocks"
        )
        
        return top_results
    
    def _calculate_data_quality_score(self, data: pd.DataFrame) -> float:
        """
        Calculate a data quality score based on completeness and consistency.
        
        Args:
            data: Stock data DataFrame
            
        Returns:
            Quality score between 0 and 1
        """
        try:
            if data.empty:
                return 0.0
            
            score = 1.0
            
            # Check for missing values
            missing_ratio = data.isnull().sum().sum() / (len(data) * len(data.columns))
            score -= missing_ratio * 0.3
            
            # Check for price consistency
            if all(col in data.columns for col in ['最高', '最低', '开盘', '收盘']):
                # Check if high >= low
                invalid_range = (data['最高'] < data['最低']).sum()
                score -= (invalid_range / len(data)) * 0.3
                
                # Check if open/close are within high/low range
                invalid_ohlc = (
                    (data['开盘'] > data['最高']) | (data['开盘'] < data['最低']) |
                    (data['收盘'] > data['最高']) | (data['收盘'] < data['最低'])
                ).sum()
                score -= (invalid_ohlc / len(data)) * 0.2
            
            # Check for extreme values (more than 50% daily change)
            if '收盘' in data.columns and len(data) > 1:
                extreme_changes = (data['收盘'].pct_change().abs() > 0.5).sum()
                score -= (extreme_changes / len(data)) * 0.2
            
            return max(0.0, score)
            
        except Exception:
            return 0.5  # Default score if calculation fails
    
    def get_analysis_summary(self, results: List[AnalysisResult]) -> Dict[str, Any]:
        """
        Generate summary statistics for analysis results.
        
        Args:
            results: List of analysis results
            
        Returns:
            Summary statistics
        """
        if not results:
            return {'total_stocks': 0, 'message': 'No valid analysis results'}
        
        amplitudes = [r.avg_amplitude for r in results]
        trading_days = [r.trading_days for r in results]
        
        return {
            'total_stocks': len(results),
            'amplitude_stats': {
                'mean': np.mean(amplitudes),
                'std': np.std(amplitudes),
                'min': np.min(amplitudes),
                'max': np.max(amplitudes),
                'median': np.median(amplitudes)
            },
            'trading_days_stats': {
                'mean': np.mean(trading_days),
                'min': np.min(trading_days),
                'max': np.max(trading_days)
            },
            'top_stock': {
                'code': results[0].stock_code,
                'name': results[0].stock_name,
                'amplitude': results[0].avg_amplitude
            } if results else None
        }
